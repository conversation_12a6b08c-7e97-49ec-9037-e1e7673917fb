<view class="debug-container data-v-3ac421fa"><view class="header data-v-3ac421fa"><text class="title data-v-3ac421fa">🔍 时间段同步调试</text><text class="subtitle data-v-3ac421fa">专门调试前后端同步问题</text></view><view class="control-panel data-v-3ac421fa"><view class="input-group data-v-3ac421fa"><text class="label data-v-3ac421fa">场馆ID:</text><input type="number" class="input data-v-3ac421fa" value="{{a}}" bindinput="{{b}}"/></view><view class="input-group data-v-3ac421fa"><text class="label data-v-3ac421fa">日期:</text><input type="date" class="input data-v-3ac421fa" value="{{c}}" bindinput="{{d}}"/></view><view class="button-group data-v-3ac421fa"><button bindtap="{{e}}" class="btn primary data-v-3ac421fa">🔧 直接调用生成API</button><button bindtap="{{f}}" class="btn secondary data-v-3ac421fa">📋 直接查询时间段</button><button bindtap="{{g}}" class="btn success data-v-3ac421fa">🔄 测试完整流程</button><button bindtap="{{h}}" class="btn warning data-v-3ac421fa">🗑️ 清除日志</button></view></view><view class="logs-section data-v-3ac421fa"><text class="section-title data-v-3ac421fa">调试日志</text><scroll-view class="logs-container data-v-3ac421fa" scroll-y><view wx:for="{{i}}" wx:for-item="log" wx:key="e" class="{{['data-v-3ac421fa', 'log-item', log.f]}}"><text class="log-time data-v-3ac421fa">{{log.a}}</text><text class="log-message data-v-3ac421fa">{{log.b}}</text><view wx:if="{{log.c}}" class="log-data data-v-3ac421fa"><text class="data-content data-v-3ac421fa">{{log.d}}</text></view></view></scroll-view></view><view class="status-section data-v-3ac421fa"><text class="section-title data-v-3ac421fa">当前状态</text><view class="status-item data-v-3ac421fa"><text class="status-label data-v-3ac421fa">后端时间段数量:</text><text class="status-value data-v-3ac421fa">{{j}}</text></view><view class="status-item data-v-3ac421fa"><text class="status-label data-v-3ac421fa">前端时间段数量:</text><text class="status-value data-v-3ac421fa">{{k}}</text></view><view class="status-item data-v-3ac421fa"><text class="status-label data-v-3ac421fa">最后操作:</text><text class="status-value data-v-3ac421fa">{{l}}</text></view></view></view>