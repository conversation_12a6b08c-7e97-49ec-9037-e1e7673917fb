"use strict";
const common_vendor = require("../common/vendor.js");
const utils_countdown = require("../utils/countdown.js");
const _sfc_main = {
  name: "CountdownTimer",
  props: {
    // 订单对象
    order: {
      type: Object,
      required: true
    },
    // 显示标签
    label: {
      type: String,
      default: "自动取消"
    },
    // 是否使用简短格式
    short: {
      type: Boolean,
      default: false
    },
    // 更新间隔（毫秒）
    interval: {
      type: Number,
      default: 1e3
    }
  },
  data() {
    return {
      countdown: null,
      timerId: null,
      showCountdown: false,
      countdownText: "",
      countdownClass: ""
    };
  },
  mounted() {
    var _a;
    console.log("CountdownTimer组件mounted，订单:", (_a = this.order) == null ? void 0 : _a.orderNo);
    this.initCountdown();
  },
  beforeDestroy() {
    this.cleanup();
  },
  watch: {
    order: {
      handler() {
        this.initCountdown();
      },
      deep: true
    }
  },
  methods: {
    // 初始化倒计时
    initCountdown() {
      var _a;
      console.log("CountdownTimer初始化，订单:", (_a = this.order) == null ? void 0 : _a.orderNo);
      this.cleanup();
      if (!utils_countdown.shouldShowCountdown(this.order)) {
        this.showCountdown = false;
        return;
      }
      this.showCountdown = true;
      this.updateCountdown();
      this.timerId = utils_countdown.createCountdownTimer(() => {
        this.updateCountdown();
      }, this.interval);
    },
    // 更新倒计时
    updateCountdown() {
      const countdownInfo = utils_countdown.getSharingOrderCountdown(this.order);
      this.countdown = countdownInfo;
      if (!countdownInfo.showCountdown) {
        this.showCountdown = false;
        return;
      }
      if (this.short) {
        this.countdownText = utils_countdown.formatCountdownShort(countdownInfo);
      } else {
        this.countdownText = countdownInfo.formatted;
      }
      this.countdownClass = utils_countdown.getCountdownClass(countdownInfo);
      if (countdownInfo.isExpired) {
        this.cleanup();
        this.$emit("expired", this.order);
        if (this.order) {
          console.log("[CountdownTimer] 触发全局订单过期事件:", this.order.orderNo);
          common_vendor.index.$emit("order-expired", {
            orderId: this.order.id,
            orderNo: this.order.orderNo,
            venueId: this.order.venueId,
            date: this.order.bookingDate,
            timeSlotIds: this.order.timeSlotIds || [],
            orderType: this.order.bookingType || "EXCLUSIVE"
          });
        }
      }
    },
    // 清理资源
    cleanup() {
      if (this.timerId) {
        utils_countdown.clearCountdownTimer(this.timerId);
        this.timerId = null;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showCountdown
  }, $data.showCountdown ? {
    b: common_vendor.t($props.label),
    c: common_vendor.t($data.countdownText),
    d: common_vendor.n($data.countdownClass)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-357e1131"]]);
wx.createComponent(Component);
