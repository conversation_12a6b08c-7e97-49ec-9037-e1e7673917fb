<view class="test-container data-v-50fe2e62"><view class="test-header data-v-50fe2e62"><text class="test-title data-v-50fe2e62">时间段显示修复测试</text><text class="test-subtitle data-v-50fe2e62">验证非今日日期时间段显示是否正常</text></view><view class="test-section data-v-50fe2e62"><text class="section-title data-v-50fe2e62">测试场景</text><view class="test-item data-v-50fe2e62"><text class="test-label data-v-50fe2e62">当前日期:</text><text class="test-value data-v-50fe2e62">{{a}}</text></view><view class="test-item data-v-50fe2e62"><text class="test-label data-v-50fe2e62">测试日期:</text><text class="test-value data-v-50fe2e62">{{b}}</text></view></view><view class="test-section data-v-50fe2e62"><text class="section-title data-v-50fe2e62">修复验证</text><button bindtap="{{c}}" class="test-button data-v-50fe2e62">测试时间段过期检查</button><button bindtap="{{d}}" class="test-button data-v-50fe2e62">测试非今日时间段</button><button bindtap="{{e}}" class="test-button clear-btn data-v-50fe2e62">清空日志</button></view><view class="test-section data-v-50fe2e62"><text class="section-title data-v-50fe2e62">测试结果</text><scroll-view class="log-container data-v-50fe2e62" scroll-y><view wx:for="{{f}}" wx:for-item="log" wx:key="c" class="log-item data-v-50fe2e62"><text class="{{['log-text', 'data-v-50fe2e62', log.b]}}">{{log.a}}</text></view></scroll-view></view></view>