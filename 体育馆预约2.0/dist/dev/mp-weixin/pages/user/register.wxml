<view class="container data-v-239527a3"><view class="header data-v-239527a3"><text class="title data-v-239527a3">注册账号</text><text class="subtitle data-v-239527a3">加入我们，开启运动之旅</text></view><view class="register-form data-v-239527a3"><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">手机号 <text class="required data-v-239527a3">*</text></text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">📱</text><input class="input-field data-v-239527a3" type="number" placeholder="请输入手机号" maxlength="11" value="{{a}}" bindinput="{{b}}"/></view></view><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">验证码 <text class="required data-v-239527a3">*</text></text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">💬</text><input class="input-field data-v-239527a3" type="number" placeholder="请输入验证码" maxlength="6" value="{{c}}" bindinput="{{d}}"/><button class="sms-btn data-v-239527a3" disabled="{{f}}" bindtap="{{g}}">{{e}}</button></view></view><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">密码 <text class="required data-v-239527a3">*</text></text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">🔒</text><input class="input-field data-v-239527a3" password="{{h}}" placeholder="请设置密码（6-20位）" maxlength="20" value="{{i}}" bindinput="{{j}}"/><text class="password-toggle data-v-239527a3" bindtap="{{l}}">{{k}}</text></view><view class="password-tips data-v-239527a3"><text class="{{['tip-item', 'data-v-239527a3', m && 'valid']}}">• 长度6-20位</text><text class="{{['tip-item', 'data-v-239527a3', n && 'valid']}}">• 包含字母</text><text class="{{['tip-item', 'data-v-239527a3', o && 'valid']}}">• 包含数字</text></view></view><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">确认密码 <text class="required data-v-239527a3">*</text></text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">🔒</text><input class="input-field data-v-239527a3" password="{{p}}" placeholder="请再次输入密码" maxlength="20" value="{{q}}" bindinput="{{r}}"/><text class="password-toggle data-v-239527a3" bindtap="{{t}}">{{s}}</text></view><view wx:if="{{v}}" class="error-tip data-v-239527a3"> 两次输入的密码不一致 </view></view><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">昵称</text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">👤</text><input class="input-field data-v-239527a3" placeholder="请输入昵称（选填）" maxlength="20" value="{{w}}" bindinput="{{x}}"/></view></view><view class="form-item data-v-239527a3"><text class="item-label data-v-239527a3">邀请码</text><view class="input-wrapper data-v-239527a3"><text class="input-icon data-v-239527a3">🎁</text><input class="input-field data-v-239527a3" placeholder="请输入邀请码（选填）" maxlength="10" value="{{y}}" bindinput="{{z}}"/></view></view><view class="agreement-section data-v-239527a3"><view class="agreement-item data-v-239527a3" bindtap="{{E}}"><view class="{{['checkbox', 'data-v-239527a3', B && 'checked']}}"><text wx:if="{{A}}" class="check-icon data-v-239527a3">✓</text></view><text class="agreement-text data-v-239527a3"> 我已阅读并同意 <text class="agreement-link data-v-239527a3" catchtap="{{C}}">《用户协议》</text> 和 <text class="agreement-link data-v-239527a3" catchtap="{{D}}">《隐私政策》</text></text></view></view><button class="register-btn data-v-239527a3" disabled="{{F}}" bindtap="{{G}}"> 注册 </button></view><view class="footer data-v-239527a3"><text class="footer-text data-v-239527a3">已有账号？</text><text class="footer-link data-v-239527a3" bindtap="{{H}}">立即登录</text></view></view>