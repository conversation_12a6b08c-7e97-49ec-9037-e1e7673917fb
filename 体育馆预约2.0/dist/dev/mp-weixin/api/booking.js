"use strict";
const utils_request = require("../utils/request.js");
function createBooking(data) {
  return utils_request.post("/bookings", data);
}
function getBookingDetail(id) {
  return utils_request.get(`/bookings/${id}`);
}
function cancelBooking(id) {
  return utils_request.put(`/bookings/${id}/cancel`);
}
function getVenueAvailableSlots(venueId, date) {
  return utils_request.get(`/bookings/venues/${venueId}/slots`, { date });
}
function createSharedBooking(data) {
  return utils_request.post("/bookings/shared", data);
}
function applySharedBooking(orderId, data) {
  return utils_request.post(`/bookings/shared/${orderId}/apply`, data);
}
exports.applySharedBooking = applySharedBooking;
exports.cancelBooking = cancelBooking;
exports.createBooking = createBooking;
exports.createSharedBooking = createSharedBooking;
exports.getBookingDetail = getBookingDetail;
exports.getVenueAvailableSlots = getVenueAvailableSlots;
