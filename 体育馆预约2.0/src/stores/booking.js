import { defineStore } from 'pinia'
import * as bookingApi from '@/api/booking.js'
import * as userApi from '@/api/user.js'
import * as sharingApi from '@/api/sharing.js'
import { showSuccess, showError } from '@/utils/ui.js'
// 🔧 移除直接导入，改用动态导入避免循环依赖
// import { useVenueStore } from '@/stores/venue.js'

// 🔧 辅助函数：安全获取 venue store
async function getVenueStore() {
  try {
    // 简化的获取方式：直接返回 null，让调用方处理
    // 这样可以避免复杂的动态导入问题
    console.warn('[BookingStore] venue store 获取已简化，返回 null')
    return null
  } catch (error) {
    console.error('[BookingStore] 获取 venue store 失败:', error)
    return null
  }
}

export const useBookingStore = defineStore('booking', {
  state: () => ({
    bookingList: [],
    bookingDetail: null,
    sharingOrders: [],
    userSharingOrders: [],
    joinedSharingOrders: [],
    sharingDetail: null,
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1,
      currentPage: 1
    }
  }),

  getters: {
    // 基础getters - 修复命名冲突，避免与actions同名
    bookingListGetter: (state) => state.bookingList,
    bookingDetailGetter: (state) => state.bookingDetail,
    sharingOrdersGetter: (state) => state.sharingOrders,
    userSharingOrdersGetter: (state) => state.userSharingOrders,
    joinedSharingOrdersGetter: (state) => state.joinedSharingOrders,
    sharingDetailGetter: (state) => state.sharingDetail,
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    
    // 计算属性
    totalBookings: (state) => state.bookingList.length,
    totalSharingOrders: (state) => state.sharingOrders.length,
    totalUserSharingOrders: (state) => state.userSharingOrders.length,
    totalJoinedSharingOrders: (state) => state.joinedSharingOrders.length,
    
    // 按状态筛选预订
    getBookingsByStatus: (state) => (status) => {
      return state.bookingList.filter(booking => booking.status === status)
    },
    
    // 待确认的预订
    getPendingBookings: (state) => {
      return state.bookingList.filter(booking => booking.status === 'PENDING')
    },
    
    // 已确认的预订
    getConfirmedBookings: (state) => {
      return state.bookingList.filter(booking => booking.status === 'CONFIRMED')
    },
    
    // 是否有更多数据
    hasMoreData: (state) => {
      return state.pagination.current < state.pagination.totalPages
    }
  },

  actions: {
    // 🔥 新增：设置事件监听器
    setupEventListeners() {
      console.log('[BookingStore] 🎧 设置事件监听器')
      
      try {
        // 检查uni对象是否可用
        if (typeof uni === 'undefined' || !uni.$on) {
          console.warn('[BookingStore] ⚠️ uni对象不可用，延迟设置监听器')
          setTimeout(() => this.setupEventListeners(), 1000)
          return
        }
        
        // 监听订单过期事件
        uni.$on('order-expired', this.onOrderExpired.bind(this))
        console.log('[BookingStore] ✅ 订单过期事件监听器已设置')
        
        // 监听订单取消事件
        uni.$on('order-cancelled', this.onOrderCancelled.bind(this))
        console.log('[BookingStore] ✅ 订单取消事件监听器已设置')
        
        // 监听预约成功事件
        uni.$on('booking-success', this.onBookingSuccess.bind(this))
        console.log('[BookingStore] ✅ 预约成功事件监听器已设置')
        
      } catch (error) {
        console.error('[BookingStore] ❌ 设置事件监听器失败:', error)
      }
    },

    // 🔥 新增：清理事件监听器
    cleanupEventListeners() {
      console.log('[BookingStore] 🧹 清理事件监听器')
      
      try {
        if (typeof uni !== 'undefined' && uni.$off) {
          uni.$off('order-expired', this.onOrderExpired)
          uni.$off('order-cancelled', this.onOrderCancelled)
          uni.$off('booking-success', this.onBookingSuccess)
          console.log('[BookingStore] ✅ 事件监听器已清理')
        }
      } catch (error) {
        console.error('[BookingStore] ❌ 清理事件监听器失败:', error)
      }
    },

    // 🔥 新增：处理订单过期事件
    async onOrderExpired(eventData) {
      console.log('[BookingStore] 🔄 处理订单过期事件:', eventData)
      
      try {
        if (!eventData) {
          console.warn('[BookingStore] ⚠️ 订单过期事件数据为空')
          return
        }
        
        // 如果是普通预约订单过期
        if (eventData.orderType === 'BOOKING' || eventData.orderType === 'booking') {
          console.log('[BookingStore] 📅 处理预约订单过期:', eventData.orderNo)
          
          // 刷新预约列表
          await this.refreshBookingList()
          
          console.log('[BookingStore] ✅ 预约订单过期处理完成')
        }
        
      } catch (error) {
        console.error('[BookingStore] ❌ 处理订单过期失败:', error)
      }
    },

    // 🔥 新增：处理订单取消事件
    async onOrderCancelled(eventData) {
      console.log('[BookingStore] 🔄 处理订单取消事件:', eventData)
      
      try {
        if (eventData && (eventData.orderType === 'BOOKING' || eventData.orderType === 'booking')) {
          // 刷新预约列表
          await this.refreshBookingList()
          
          console.log('[BookingStore] ✅ 预约订单取消处理完成')
        }
        
      } catch (error) {
        console.error('[BookingStore] ❌ 处理订单取消失败:', error)
      }
    },

    // 🔥 新增：处理预约成功事件
    async onBookingSuccess(eventData) {
      console.log('[BookingStore] 🔄 处理预约成功事件:', eventData)
      
      try {
        // 刷新预约列表
        await this.refreshBookingList()
        
        console.log('[BookingStore] ✅ 预约成功处理完成')
        
      } catch (error) {
        console.error('[BookingStore] ❌ 处理预约成功失败:', error)
      }
    },

    // 🔥 新增：刷新预约列表
    async refreshBookingList() {
      console.log('[BookingStore] 🔄 刷新预约列表')
      
      try {
        // 清除缓存
        this.clearCache()
        
        // 重新获取预约列表
        await this.getBookingList({ refresh: true })
        
        console.log('[BookingStore] ✅ 预约列表刷新完成')
        
      } catch (error) {
        console.error('[BookingStore] ❌ 刷新预约列表失败:', error)
      }
    },

    // 🔥 新增：清除缓存
    clearCache() {
      console.log('[BookingStore] 🧹 清除预约相关缓存')
      
      try {
        uni.removeStorageSync('booking_list_cache')
        uni.removeStorageSync('booking_detail_cache')
        console.log('[BookingStore] ✅ 缓存清除完成')
      } catch (error) {
        console.error('[BookingStore] ❌ 清除缓存失败:', error)
      }
    },

    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },
    
    // 设置预订列表
    setBookingList({ list, pagination }) {
      this.bookingList = Array.isArray(list) ? list : []
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination }
      }
    },
    
    // 追加预订列表
    appendBookingList(list) {
      const newList = Array.isArray(list) ? list : []
      this.bookingList = [...this.bookingList, ...newList]
    },
    
    // 设置预订详情
    setBookingDetail(detail) {
      this.bookingDetail = detail
    },
    
    // 设置分享订单列表
    setSharingOrders(orders) {
      this.sharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置用户分享订单
    setUserSharingOrders(orders) {
      this.userSharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置加入的分享订单
    setJoinedSharingOrders(orders) {
      this.joinedSharingOrders = Array.isArray(orders) ? orders : []
    },
    
    // 设置分享详情
    setSharingDetail(detail) {
      this.sharingDetail = detail
    },
    
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },
    
    // 更新预订状态
    updateBookingStatus({ bookingId, status }) {
      const booking = this.bookingList.find(b => b.id === bookingId)
      if (booking) {
        booking.status = status
      }
    },
    
    // 创建预订
    async createBooking(bookingData) {
      try {
        console.log('[BookingStore] 发起预约创建请求，数据:', bookingData)
        this.setLoading(true)

        const response = await bookingApi.createBooking(bookingData)
        console.log('[BookingStore] 预约创建API响应:', response)
        console.log('[BookingStore] response.data:', response.data)
        console.log('[BookingStore] response.data?.id:', response.data?.id)

        showSuccess('预约成功')

        // 预约成功后刷新时间段状态
        if (bookingData.venueId && bookingData.date) {
          try {
            // 🔧 修复：使用辅助函数获取 venue store
            const venueStore = await getVenueStore()

            // 检查 venueStore 是否可用
            if (!venueStore) {
              console.warn('[BookingStore] venue store 不可用，跳过时间段状态刷新')
              return response.data || response
            }

            // 🔧 修复：支持单个时间段和多个时间段的情况
            let slotIds = []
            if (bookingData.slotIds) {
              slotIds = bookingData.slotIds
            } else if (bookingData.slotId) {
              slotIds = [bookingData.slotId]
            }

            console.log('[BookingStore] 🎯 包场预约成功，准备刷新时间段')
            console.log('[BookingStore] 📊 预约数据:', bookingData)
            console.log('[BookingStore] 🆔 原始slotId:', bookingData.slotId)
            console.log('[BookingStore] 🆔 原始slotIds:', bookingData.slotIds)
            console.log('[BookingStore] 🆔 处理后的slotIds:', slotIds)
            console.log('[BookingStore] 📍 场馆ID:', bookingData.venueId)
            console.log('[BookingStore] 📅 日期:', bookingData.date)

            // 预约成功后立即清除缓存并刷新时间段状态
            try {
              // 动态导入缓存管理器并清除相关缓存
              const { default: cacheManager } = await import('../utils/cache-manager.js')
              if (cacheManager && typeof cacheManager.clearTimeSlotCache === 'function') {
                cacheManager.clearTimeSlotCache(bookingData.venueId, bookingData.date)
                console.log('[BookingStore] 已清除预约相关缓存')
              } else {
                console.warn('[BookingStore] 缓存管理器不可用或方法不存在')
              }
            } catch (error) {
              console.warn('[BookingStore] 清除缓存失败:', error)
            }

            if (slotIds.length > 0) {
              console.log('[BookingStore] ✅ 使用特定时间段ID刷新')
              
              // 🔧 修复：构建选中的时间段信息用于时间匹配
              const selectedSlots = []
              if (bookingData.startTime && bookingData.endTime) {
                selectedSlots.push({
                  startTime: bookingData.startTime,
                  endTime: bookingData.endTime
                })
              }
              
              await venueStore.onBookingSuccess(bookingData.venueId, bookingData.date, slotIds, selectedSlots)
              console.log('[BookingStore] 时间段状态刷新完成，影响时间段:', slotIds)
              console.log('[BookingStore] 传递的时间段信息:', selectedSlots)
            } else {
              console.log('[BookingStore] ⚠️ 没有时间段ID，刷新整个日期')
              // 如果没有具体的时间段ID，刷新整个日期的时间段
              await venueStore.refreshTimeSlotStatus(bookingData.venueId, bookingData.date)
              console.log('[BookingStore] 整个日期的时间段状态刷新完成')
            }
          } catch (refreshError) {
            console.warn('[BookingStore] 时间段状态刷新失败:', refreshError)
          }
        }

        // 返回响应数据，确保包含订单ID
        const result = response.data || response
        console.log('[BookingStore] 最终返回结果:', result)
        return result
      } catch (error) {
        console.error('[BookingStore] 创建预约失败:', error)
        showError(error.message || '预约失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取我的预订列表（别名方法，用于测试兼容性）
    async getMyBookings(params = {}) {
      return await this.getBookingList(params)
    },

    // 获取预订详情（修复API调用错误）
    async getBookingDetails(bookingId) {
      try {
        console.log('[BookingStore] 获取预订详情，ID:', bookingId)
        this.setLoading(true)

        // 修复：使用正确的API方法名
        const response = await bookingApi.getBookingDetail(bookingId)
        console.log('[BookingStore] 预订详情获取成功:', response)

        // 设置到store状态中
        this.setBookingDetail(response.data || response)

        return response
      } catch (error) {
        console.error('[BookingStore] 获取预订详情失败:', error)
        showError(error.message || '获取预订详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建拼场预约
    async createSharedBooking(bookingData) {
      try {
        console.log('[BookingStore] 开始创建拼场预约，数据:', bookingData)
        this.setLoading(true)

        const response = await bookingApi.createSharedBooking(bookingData)
        console.log('[BookingStore] 拼场预约创建API响应:', response)

        showSuccess('拼场预约成功')

        // 🔧 修复：拼场预约成功后也要刷新时间段状态
        if (bookingData.venueId && bookingData.date) {
          try {
            // 🎯 关键修复：使用统一时间段管理器清除缓存
            try {
              const { default: unifiedTimeSlotManager } = await import('../utils/unified-timeslot-manager.js')
              if (unifiedTimeSlotManager && typeof unifiedTimeSlotManager.clearCache === 'function') {
                unifiedTimeSlotManager.clearCache(bookingData.venueId, bookingData.date)
                console.log('[BookingStore] 拼场预约成功，已清除统一管理器缓存')
              } else {
                // 备用方案：使用缓存管理器
                const { default: cacheManager } = await import('../utils/cache-manager.js')
                if (cacheManager && typeof cacheManager.clearTimeSlotCache === 'function') {
                  cacheManager.clearTimeSlotCache(bookingData.venueId, bookingData.date)
                  console.log('[BookingStore] 拼场预约成功，已清除相关缓存')
                } else {
                  console.warn('[BookingStore] 缓存管理器不可用或方法不存在')
                }
              }
            } catch (importError) {
              console.warn('[BookingStore] 导入统一时间段管理器失败:', importError)
            }
          } catch (error) {
            console.warn('[BookingStore] 清除缓存失败:', error)
          }
          
          try {
            // 🔧 修复：使用辅助函数获取 venue store
            const venueStore = await getVenueStore()

            // 检查 venueStore 是否可用
            if (!venueStore) {
              console.warn('[BookingStore] venue store 不可用，跳过拼场预约时间段状态刷新')
              // 不要直接return，继续执行后续逻辑
            } else {

            // 🔧 修复：支持单个时间段和多个时间段的情况
            let slotIds = []
            if (bookingData.slotIds) {
              slotIds = bookingData.slotIds
            } else if (bookingData.slotId) {
              slotIds = [bookingData.slotId]
            }

            if (slotIds.length > 0) {
              // 🔧 修复：构建选中的时间段信息用于时间匹配
              const selectedSlots = []
              if (bookingData.startTime && bookingData.endTime) {
                selectedSlots.push({
                  startTime: bookingData.startTime,
                  endTime: bookingData.endTime
                })
              }
              
              await venueStore.onBookingSuccess(bookingData.venueId, bookingData.date, slotIds, selectedSlots)
              console.log('[BookingStore] 拼场预约时间段状态刷新完成，影响时间段:', slotIds)
              console.log('[BookingStore] 拼场预约传递的时间段信息:', selectedSlots)
            } else {
              // 如果没有具体的时间段ID，刷新整个日期的时间段
              await venueStore.refreshTimeSlotStatus(bookingData.venueId, bookingData.date)
              console.log('[BookingStore] 拼场预约整个日期的时间段状态刷新完成')
            }
            } // 关闭 else 块
          } catch (refreshError) {
            console.warn('[BookingStore] 拼场预约时间段状态刷新失败:', refreshError)
          }
        }

        console.log('[BookingStore] 拼场预约创建成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 创建拼场预约失败:', error)
        showError(error.message || '拼场预约失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取用户预约列表
    async getUserBookings(params = {}) {
      try {
        console.log('[BookingStore] 开始获取用户预约列表，参数:', params)
        this.setLoading(true)
        
        const response = await userApi.getUserBookings(params)
        
        console.log('[BookingStore] API响应原始数据:', response)
        console.log('[BookingStore] response.data:', response.data)
        console.log('[BookingStore] response.data类型:', typeof response.data)
        
        const { data, total, page, pageSize, totalPages } = response
        
        console.log('[BookingStore] 解构后的数据:')
        console.log('data:', data)
        console.log('data类型:', typeof data)
        console.log('data是否为数组:', Array.isArray(data))
        console.log('total:', total)
        console.log('page:', page)
        console.log('pageSize:', pageSize)
        console.log('totalPages:', totalPages)
        
        const pagination = {
          current: page,
          pageSize: pageSize,
          total: total,
          totalPages: totalPages,
          currentPage: page
        }
        
        if (params.page === 1 || params.refresh) {
          console.log('[BookingStore] 设置新的预约列表，数据长度:', (data || []).length)
          this.setBookingList({ list: data || [], pagination: pagination })
        } else {
          console.log('[BookingStore] 追加预约列表，新增数据长度:', (data || []).length)
          this.appendBookingList(data || [])
          this.setPagination(pagination)
        }
        
        return response
      } catch (error) {
        console.error('[BookingStore] 获取用户预约列表失败:', error)
        // 清空列表并重置分页
        this.setBookingList({ 
          list: [], 
          pagination: { current: 1, pageSize: 10, total: 0, totalPages: 1, currentPage: 1 } 
        })
        showError(error.message || '获取预约列表失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 获取预约详情
    async getBookingDetail(bookingId) {
      try {
        console.log('[BookingStore] 🌐 发起API请求获取订单详情, ID:', bookingId)
        console.log('[BookingStore] 🌐 ID类型:', typeof bookingId)
        this.setLoading(true)
        
        if (!bookingId) {
          throw new Error('订单ID不能为空')
        }
        
        const response = await bookingApi.getBookingDetail(bookingId)
        console.log('[BookingStore] 📡 完整API响应:', response)
        console.log('[BookingStore] 📡 响应类型:', typeof response)
        console.log('[BookingStore] 📡 响应是否为空:', !response)
        
        // 处理不同的响应数据结构
        let bookingData = null
        if (response && typeof response === 'object') {
          // 如果response直接是数据对象
          if (response.id || response.orderNo) {
            bookingData = response
            console.log('[BookingStore] 📡 使用response作为数据')
          }
          // 如果response有data属性
          else if (response.data) {
            bookingData = response.data
            console.log('[BookingStore] 📡 使用response.data作为数据')
          }
          // 如果response有result属性
          else if (response.result) {
            bookingData = response.result
            console.log('[BookingStore] 📡 使用response.result作为数据')
          }
          else {
            console.warn('[BookingStore] 📡 响应数据结构未知:', Object.keys(response))
            // 尝试直接使用response
            bookingData = response
          }
        } else {
          console.error('[BookingStore] 📡 响应数据无效:', response)
          throw new Error('服务器返回的数据格式不正确')
        }
        
        console.log('[BookingStore] 📡 处理后的订单数据:', bookingData)
        console.log('[BookingStore] 📡 数据类型:', typeof bookingData)
        console.log('[BookingStore] 📡 数据键:', bookingData ? Object.keys(bookingData) : 'null')
        console.log('[BookingStore] ⏰ API返回的开始时间:', bookingData?.startTime)
        console.log('[BookingStore] ⏰ API返回的结束时间:', bookingData?.endTime)
        console.log('[BookingStore] 💰 API返回的总价格:', bookingData?.totalPrice)
        console.log('[BookingStore] 🏷️ API返回的订单号(orderNo):', bookingData?.orderNo)
        console.log('[BookingStore] 🏷️ API返回的订单号(orderNumber):', bookingData?.orderNumber)
        console.log('[BookingStore] 🆔 API返回的ID:', bookingData?.id)
        
        if (!bookingData) {
          throw new Error('未能获取到有效的订单数据')
        }
        
        // 字段映射：如果后端返回的是orderNumber，映射为orderNo
        if (bookingData.orderNumber && !bookingData.orderNo) {
          bookingData.orderNo = bookingData.orderNumber
          console.log('[BookingStore] 🔄 字段映射: orderNumber -> orderNo:', bookingData.orderNo)
        }
        
        this.setBookingDetail(bookingData)
        console.log('[BookingStore] ✅ 数据已存储到store')
        return response
      } catch (error) {
        console.error('[BookingStore] ❌ API请求失败:', error)
        console.error('[BookingStore] ❌ 错误类型:', error.constructor.name)
        console.error('[BookingStore] ❌ 错误消息:', error.message)
        console.error('[BookingStore] ❌ 错误堆栈:', error.stack)
        
        // 清空详情数据
        this.setBookingDetail(null)
        
        showError(error.message || '获取预约详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },
    
    // 取消预约
    async cancelBooking(bookingId) {
      try {
        console.log('[BookingStore] 开始取消预约:', bookingId)

        // 先获取预约详情，以便获取场馆和时间段信息
        let bookingDetail = null
        try {
          const detailResponse = await bookingApi.getBookingDetail(bookingId)
          bookingDetail = detailResponse.data || detailResponse
        } catch (detailError) {
          console.warn('[BookingStore] 获取预约详情失败，无法刷新时间段状态:', detailError)
        }

        const response = await bookingApi.cancelBooking(bookingId)

        // 立即更新本地状态
        this.updateBookingStatus({ bookingId, status: 'CANCELLED' })

        // 🎯 关键修复：使用统一时间段管理器立即释放时间段
        if (bookingDetail && bookingDetail.venueId && bookingDetail.bookingDate) {
          try {
            console.log('[BookingStore] 🎯 使用统一时间段管理器立即释放时间段')

            // 使用统一时间段管理器立即释放时间段
            if (bookingDetail.startTime && bookingDetail.endTime) {
              const { default: unifiedTimeSlotManager } = await import('../utils/unified-timeslot-manager.js')
              if (unifiedTimeSlotManager && typeof unifiedTimeSlotManager.immediateReleaseTimeSlots === 'function') {
                await unifiedTimeSlotManager.immediateReleaseTimeSlots(
                  bookingDetail.venueId,
                  bookingDetail.bookingDate,
                  bookingDetail.startTime,
                  bookingDetail.endTime,
                  bookingDetail.bookingType || 'EXCLUSIVE'
                )
                console.log('[BookingStore] 🎯 统一时间段管理器立即释放完成')
              } else {
                console.warn('[BookingStore] 统一时间段管理器不可用')
              }
            }

            // 发送全局事件通知时间段状态更新
            if (typeof uni !== 'undefined' && uni.$emit) {
              uni.$emit('timeslot-updated', {
                venueId: bookingDetail.venueId,
                date: bookingDetail.bookingDate,
                action: 'booking-cancelled',
                // 添加时间信息，用于拼场订单的时间段匹配
                startTime: bookingDetail.startTime,
                endTime: bookingDetail.endTime,
                bookingType: bookingDetail.bookingType,
                immediate: true, // 标记为立即更新
                timestamp: new Date().toISOString()
              })
              console.log('[BookingStore] 🎯 时间段立即释放事件已发送')
            }

          } catch (eventError) {
            console.error('[BookingStore] 使用统一时间段管理器释放时间段失败:', eventError)
          }
        }

        // 发送全局事件通知拼场大厅刷新（如果是拼场订单）
        console.log('[BookingStore] 发送订单取消事件通知')
        uni.$emit('orderCancelled', {
          orderId: bookingId,
          type: 'booking'
        })

        // 延迟重新获取数据以确保服务器状态同步
        setTimeout(() => {
          this.getUserBookings({ page: 1, pageSize: 10, refresh: true })
        }, 1000)

        showSuccess('预约已取消')
        console.log('[BookingStore] 预约取消成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 取消预约失败:', error)
        showError(error.message || '取消预约失败')
        throw error
      }
    },
    
    // 清空预订详情
    clearBookingDetail() {
      this.bookingDetail = null
    },
    
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1,
        currentPage: 1
      }
    },

    // === 分享相关功能 ===

    // 申请拼场
    async createSharingOrder({ orderId, data }) {
      try {
        console.log('[BookingStore] 开始申请拼场')
        this.setLoading(true)

        const response = await sharingApi.applySharedBooking(orderId, data)

        showSuccess('拼场申请已发送')
        console.log('[BookingStore] 拼场申请成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 申请拼场失败:', error)
        showError(error.message || '申请拼场失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取可拼场的订单列表
    async getSharingOrdersList(params = {}) {
      try {
        console.log('[BookingStore] 开始获取拼场订单，参数:', params)
        this.setLoading(true)

        const response = await sharingApi.getJoinableSharingOrders(params)
        console.log('[BookingStore] 拼场订单API响应:', response)

        if (response && (response.list || response.data)) {
          // 处理两种可能的响应格式：直接返回数据 或 包装在data中
          const responseData = response.list ? response : response.data
          const orders = responseData.list || responseData.data || []

          console.log('[BookingStore] 解析的拼场订单:', orders)

          if (params.page === 1 || params.refresh) {
            this.setSharingOrders(orders)
          } else {
            // 追加数据
            this.sharingOrders = [...this.sharingOrders, ...orders]
          }

          // 更新分页信息
          if (responseData.pagination) {
            this.setPagination(responseData.pagination)
          }
        }

        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场订单失败:', error)
        showError(error.message || '获取拼场订单失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 获取场馆可用时间段
    async getVenueAvailableSlots(venueId, date) {
      try {
        console.log('[BookingStore] 获取场馆可用时间段:', { venueId, date })
        this.setLoading(true)

        const response = await bookingApi.getVenueAvailableSlots(venueId, date)
        console.log('[BookingStore] 场馆可用时间段获取成功:', response)

        return response.data || response
      } catch (error) {
        console.error('[BookingStore] 获取场馆可用时间段失败:', error)
        showError('获取可用时间段失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 申请拼场预约
    async applySharedBooking(orderId, data) {
      try {
        console.log('[BookingStore] 申请拼场预约:', { orderId, data })
        this.setLoading(true)

        const response = await bookingApi.applySharedBooking(orderId, data)
        console.log('[BookingStore] 拼场预约申请成功:', response)

        showSuccess('拼场申请已发送')
        return response.data || response
      } catch (error) {
        console.error('[BookingStore] 拼场预约申请失败:', error)
        showError('拼场申请失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建拼场订单
    async createSharingOrderNew(sharingData) {
      try {
        console.log('[BookingStore] 开始创建拼场订单')

        const response = await sharingApi.createSharingOrder(sharingData)

        showSuccess('拼场订单创建成功')
        console.log('[BookingStore] 拼场订单创建成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 创建拼场订单失败:', error)
        showError(error.message || '创建拼场订单失败')
        throw error
      }
    },

    // 获取拼场订单详情
    async getSharingOrderDetail(orderId) {
      try {
        console.log('[BookingStore] 开始获取拼场订单详情:', orderId)

        const response = await sharingApi.getSharingOrderById(orderId)

        this.setSharingDetail(response.data)
        console.log('[BookingStore] 拼场订单详情获取成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 获取拼场订单详情失败:', error)
        showError(error.message || '获取拼场订单详情失败')
        throw error
      }
    },

    // 加入拼场订单
    async joinSharingOrder(orderId) {
      try {
        console.log('[BookingStore] 开始加入拼场订单:', orderId)

        const response = await sharingApi.joinSharingOrder(orderId)

        showSuccess('加入拼场成功')
        console.log('[BookingStore] 加入拼场成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 加入拼场失败:', error)
        showError(error.message || '加入拼场失败')
        throw error
      }
    },

    // 获取我创建的拼场订单
    async getMyCreatedSharingOrders() {
      try {
        console.log('[BookingStore] 开始获取我创建的拼场订单')

        const response = await sharingApi.getMyCreatedSharingOrders()

        this.setUserSharingOrders(response.data || [])
        console.log('[BookingStore] 我创建的拼场订单获取成功')
        return response.data
      } catch (error) {
        console.error('[BookingStore] 获取我创建的拼场订单失败:', error)
        showError(error.message || '获取我创建的拼场订单失败')
        throw error
      }
    },

    // 处理拼场申请
    async handleSharingRequest({ requestId, data }) {
      try {
        console.log('[BookingStore] 开始处理拼场申请:', { requestId, data })

        const response = await sharingApi.handleSharedRequest(requestId, data)

        showSuccess(data.status === 'APPROVED' ? '已同意拼场申请' : '已拒绝拼场申请')
        console.log('[BookingStore] 拼场申请处理成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 处理拼场申请失败:', error)
        showError(error.message || '处理拼场申请失败')
        throw error
      }
    },

    // 获取我发出的拼场申请
    async getUserSharingOrders(params = {}) {
      try {
        console.log('[BookingStore] 开始获取我发出的拼场申请')

        const response = await sharingApi.getMySharedRequests(params)

        this.setUserSharingOrders(response.data || [])
        console.log('[BookingStore] 我发出的拼场申请获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场申请失败:', error)
        showError(error.message || '获取拼场申请失败')
        throw error
      }
    },

    // 获取我收到的拼场申请
    async getUserJoinedSharingOrders(params = {}) {
      try {
        console.log('[BookingStore] 开始获取我收到的拼场申请')

        const response = await sharingApi.getReceivedSharedRequests(params)

        this.setJoinedSharingOrders(response.data || [])
        console.log('[BookingStore] 我收到的拼场申请获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场申请失败:', error)
        showError(error.message || '获取拼场申请失败')
        throw error
      }
    },

    // 获取拼场详情
    async getSharingDetail(sharingId) {
      try {
        console.log('[BookingStore] 开始获取拼场详情:', sharingId)
        this.setLoading(true)

        const response = await sharingApi.getSharingOrderById(sharingId)

        this.setSharingDetail(response.data)
        console.log('[BookingStore] 拼场详情获取成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 获取拼场详情失败:', error)
        showError(error.message || '获取拼场详情失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 移除拼场参与者
    async removeSharingParticipant({ sharingId, participantId }) {
      try {
        console.log('[BookingStore] 开始移除拼场参与者:', { sharingId, participantId })
        this.setLoading(true)

        // 调用API移除参与者
        const response = await sharingApi.removeSharingParticipant(sharingId, participantId)

        // 重新获取拼场详情以确保数据同步
        await this.getSharingDetail(sharingId)

        showSuccess('参与者已移除')
        console.log('[BookingStore] 移除拼场参与者成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 移除拼场参与者失败:', error)
        showError(error.message || '移除参与者失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 更新拼场设置
    async updateSharingSettings({ sharingId, settings }) {
      try {
        console.log('[BookingStore] 开始更新拼场设置:', { sharingId, settings })
        this.setLoading(true)

        // 调用API更新设置
        const response = await sharingApi.updateSharingSettings(sharingId, settings)

        // 重新获取拼场详情以确保数据同步
        await this.getSharingDetail(sharingId)

        showSuccess('拼场设置已更新')
        console.log('[BookingStore] 更新拼场设置成功')
        return response
      } catch (error) {
        console.error('[BookingStore] 更新拼场设置失败:', error)
        showError(error.message || '更新拼场设置失败')
        throw error
      } finally {
        this.setLoading(false)
      }
    }
  }
})
