<view class="validation-container data-v-8dbbe3e5"><view class="header data-v-8dbbe3e5"><text class="title data-v-8dbbe3e5">🔍 字段映射完整性验证</text><text class="subtitle data-v-8dbbe3e5">验证拼场、包场、时间段所有字段都与后端数据库完全匹配</text></view><view class="control-panel data-v-8dbbe3e5"><view class="input-group data-v-8dbbe3e5"><text class="label data-v-8dbbe3e5">场馆ID:</text><input type="number" class="input data-v-8dbbe3e5" value="{{a}}" bindinput="{{b}}"/></view><view class="input-group data-v-8dbbe3e5"><text class="label data-v-8dbbe3e5">测试日期:</text><input type="date" class="input data-v-8dbbe3e5" value="{{c}}" bindinput="{{d}}"/></view><view class="button-group data-v-8dbbe3e5"><button bindtap="{{e}}" class="btn primary data-v-8dbbe3e5">🏟️ 验证包场字段</button><button bindtap="{{f}}" class="btn secondary data-v-8dbbe3e5">🤝 验证拼场字段</button><button bindtap="{{g}}" class="btn success data-v-8dbbe3e5">⏰ 验证时间段字段</button><button bindtap="{{h}}" class="btn warning data-v-8dbbe3e5">🔍 全面验证</button><button bindtap="{{i}}" class="btn error data-v-8dbbe3e5">🗑️ 清除结果</button></view></view><view class="results-section data-v-8dbbe3e5"><text class="section-title data-v-8dbbe3e5">验证结果 ({{j}}项)</text><scroll-view class="results-container data-v-8dbbe3e5" scroll-y><view wx:for="{{k}}" wx:for-item="result" wx:key="n" class="{{['data-v-8dbbe3e5', 'result-item', result.o]}}"><view class="result-header data-v-8dbbe3e5"><text class="result-title data-v-8dbbe3e5">{{result.a}}</text><text class="{{['data-v-8dbbe3e5', 'result-status', result.c]}}">{{result.b}}</text><text class="result-time data-v-8dbbe3e5">{{result.d}}</text></view><view class="result-content data-v-8dbbe3e5"><text class="result-message data-v-8dbbe3e5">{{result.e}}</text><view wx:if="{{result.f}}" class="mapping-details data-v-8dbbe3e5"><text class="mapping-title data-v-8dbbe3e5">字段映射:</text><view wx:for="{{result.g}}" wx:for-item="map" wx:key="d" class="mapping-item data-v-8dbbe3e5"><text class="mapping-text data-v-8dbbe3e5">{{map.a}} → {{map.b}}: {{map.c}}</text></view><view wx:if="{{result.h}}" class="additions-section data-v-8dbbe3e5"><text class="additions-title data-v-8dbbe3e5">新增字段:</text><view wx:for="{{result.i}}" wx:for-item="add" wx:key="d" class="addition-item data-v-8dbbe3e5"><text class="addition-text data-v-8dbbe3e5">{{add.a}}: {{add.b}} ({{add.c}})</text></view></view></view><view wx:if="{{result.j}}" class="errors-section data-v-8dbbe3e5"><text class="errors-title data-v-8dbbe3e5">验证错误:</text><view wx:for="{{result.k}}" wx:for-item="error" wx:key="b" class="error-item data-v-8dbbe3e5"><text class="error-text data-v-8dbbe3e5">❌ {{error.a}}</text></view></view><view wx:if="{{result.l}}" class="warnings-section data-v-8dbbe3e5"><text class="warnings-title data-v-8dbbe3e5">验证警告:</text><view wx:for="{{result.m}}" wx:for-item="warning" wx:key="b" class="warning-item data-v-8dbbe3e5"><text class="warning-text data-v-8dbbe3e5">⚠️ {{warning.a}}</text></view></view></view></view></scroll-view></view><view wx:if="{{l}}" class="stats-section data-v-8dbbe3e5"><text class="section-title data-v-8dbbe3e5">验证统计</text><view class="stats-grid data-v-8dbbe3e5"><view class="stat-item success data-v-8dbbe3e5"><text class="stat-number data-v-8dbbe3e5">{{m}}</text><text class="stat-label data-v-8dbbe3e5">通过</text></view><view class="stat-item warning data-v-8dbbe3e5"><text class="stat-number data-v-8dbbe3e5">{{n}}</text><text class="stat-label data-v-8dbbe3e5">警告</text></view><view class="stat-item error data-v-8dbbe3e5"><text class="stat-number data-v-8dbbe3e5">{{o}}</text><text class="stat-label data-v-8dbbe3e5">失败</text></view><view class="stat-item info data-v-8dbbe3e5"><text class="stat-number data-v-8dbbe3e5">{{p}}</text><text class="stat-label data-v-8dbbe3e5">总计</text></view></view></view></view>