<view class="container data-v-46bdd483"><view class="header data-v-46bdd483"><text class="title data-v-46bdd483">🔍 Pinia迁移最终验证</text><text class="subtitle data-v-46bdd483">检查迁移后的功能完整性</text></view><view class="test-section data-v-46bdd483"><button class="test-btn primary data-v-46bdd483" bindtap="{{b}}" disabled="{{c}}">{{a}}</button></view><view wx:if="{{d}}" class="results-section data-v-46bdd483"><view class="section-title data-v-46bdd483">📊 测试结果</view><view wx:for="{{e}}" wx:for-item="result" wx:key="f" class="{{['result-item', 'data-v-46bdd483', result.g]}}"><view class="result-header data-v-46bdd483"><text class="result-icon data-v-46bdd483">{{result.a}}</text><text class="result-title data-v-46bdd483">{{result.b}}</text></view><text class="result-message data-v-46bdd483">{{result.c}}</text><text wx:if="{{result.d}}" class="result-detail data-v-46bdd483">{{result.e}}</text></view></view><view wx:if="{{f}}" class="summary-section data-v-46bdd483"><view class="summary-card data-v-46bdd483"><text class="summary-title data-v-46bdd483">📈 测试总结</text><view class="summary-stats data-v-46bdd483"><view class="stat-item success data-v-46bdd483"><text class="stat-number data-v-46bdd483">{{g}}</text><text class="stat-label data-v-46bdd483">通过</text></view><view class="stat-item warning data-v-46bdd483"><text class="stat-number data-v-46bdd483">{{h}}</text><text class="stat-label data-v-46bdd483">警告</text></view><view class="stat-item error data-v-46bdd483"><text class="stat-number data-v-46bdd483">{{i}}</text><text class="stat-label data-v-46bdd483">错误</text></view></view><text class="{{['summary-conclusion', 'data-v-46bdd483', k]}}">{{j}}</text></view></view></view>