<view class="container data-v-f3dce692"><view class="mode-switch data-v-f3dce692"><view class="{{['mode-item', 'data-v-f3dce692', a && 'active']}}" bindtap="{{b}}"> 可参与 </view><view class="{{['mode-item', 'data-v-f3dce692', c && 'active']}}" bindtap="{{d}}"> 全部 </view></view><view class="filter-section data-v-f3dce692"><scroll-view class="filter-scroll data-v-f3dce692" scroll-x><view class="{{['filter-item', 'data-v-f3dce692', e && 'active']}}" bindtap="{{f}}"> 全部 </view><view wx:for="{{g}}" wx:for-item="status" wx:key="b" class="{{['filter-item', 'data-v-f3dce692', status.c && 'active']}}" bindtap="{{status.d}}">{{status.a}}</view></scroll-view><view class="filter-more data-v-f3dce692" bindtap="{{h}}"><text class="data-v-f3dce692">筛选</text></view></view><view wx:if="{{true}}" class="debug-info data-v-f3dce692" style="background:#f0f0f0;padding:10px;margin:10px;border-radius:5px"><view class="data-v-f3dce692" style="margin-bottom:5px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">调试信息：</text></view><view class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">原始数据数量: {{i}}</text></view><view class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">筛选后数量: {{j}}</text></view><view class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">加载状态: {{k}}</text></view><view class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">选中状态: {{l}}</text></view><view class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">Store状态: {{m}}</text></view><view wx:if="{{n}}" class="data-v-f3dce692" style="margin-bottom:3px"><text class="data-v-f3dce692" style="font-size:12px;color:#666">第一条数据: {{o}}</text></view></view><view class="sharing-list data-v-f3dce692"><view wx:for="{{p}}" wx:for-item="sharing" wx:key="B" class="{{['sharing-card', 'data-v-f3dce692', sharing.C && 'full-card']}}" bindtap="{{sharing.D}}"><view class="card-header data-v-f3dce692"><view class="venue-info data-v-f3dce692"><text class="venue-name data-v-f3dce692">{{sharing.a}}</text><text class="venue-location data-v-f3dce692">📍 {{sharing.b}}</text></view><view wx:if="{{sharing.c}}" class="my-sharing-badge data-v-f3dce692"><text class="badge-text data-v-f3dce692">我的</text></view><view wx:if="{{sharing.d}}" class="full-badge data-v-f3dce692"><text class="badge-text data-v-f3dce692">已满</text></view><view class="{{['sharing-status', 'data-v-f3dce692', sharing.f]}}">{{sharing.e}}</view></view><view class="card-content data-v-f3dce692"><view class="time-info data-v-f3dce692"><text class="time-icon data-v-f3dce692">🕐</text><text class="time-text data-v-f3dce692">{{sharing.g}}</text></view><view class="team-info data-v-f3dce692"><text class="team-icon data-v-f3dce692">👥</text><text class="team-name data-v-f3dce692">{{sharing.h}}</text></view><view class="participants-info data-v-f3dce692"><text class="participants-text data-v-f3dce692">参与球队：{{sharing.i}}/{{sharing.j}}支</text><view class="progress-bar data-v-f3dce692"><view class="progress-fill data-v-f3dce692" style="{{'width:' + sharing.k}}"></view></view></view><countdown-timer wx:if="{{sharing.l}}" class="simple data-v-f3dce692" bindexpired="{{sharing.m}}" u-i="{{sharing.n}}" bind:__l="__l" u-p="{{sharing.o}}"/><view class="price-info data-v-f3dce692"><text class="price-label data-v-f3dce692">费用：</text><text class="price-value data-v-f3dce692">¥{{sharing.p}}</text><text class="price-note data-v-f3dce692">（每队费用）</text></view><view class="creator-info data-v-f3dce692"><text class="creator-label data-v-f3dce692">发起人：</text><text class="creator-value data-v-f3dce692">{{sharing.q}}</text></view><view class="create-info data-v-f3dce692"><text class="create-label data-v-f3dce692">创建时间：</text><text class="create-value data-v-f3dce692">{{sharing.r}}</text></view><view wx:if="{{sharing.s}}" class="description data-v-f3dce692"><text class="data-v-f3dce692">{{sharing.t}}</text></view></view><view class="card-actions data-v-f3dce692"><view class="organizer-info data-v-f3dce692"><text class="organizer-name data-v-f3dce692">{{sharing.v}}</text></view><button wx:if="{{sharing.w}}" class="join-btn data-v-f3dce692" catchtap="{{sharing.x}}"> 申请拼场 </button><view wx:else class="{{['join-disabled', 'data-v-f3dce692', sharing.z && 'applied', sharing.A && 'my-sharing']}}">{{sharing.y}}</view></view></view></view><view wx:if="{{q}}" class="empty-state data-v-f3dce692"><text class="empty-icon data-v-f3dce692">🏀</text><text class="empty-text data-v-f3dce692">暂无拼场订单</text></view><view wx:if="{{r}}" class="loading-state data-v-f3dce692"><text class="data-v-f3dce692">加载中...</text></view><view wx:if="{{s}}" class="load-more data-v-f3dce692" bindtap="{{v}}"><text class="data-v-f3dce692">{{t}}</text></view><uni-popup wx:if="{{H}}" class="r data-v-f3dce692" u-s="{{['d']}}" u-r="filterPopup" data-c-h="{{!G}}" u-i="f3dce692-1" bind:__l="__l" u-p="{{H}}"><view class="filter-modal data-v-f3dce692"><view class="filter-header data-v-f3dce692"><text class="filter-title data-v-f3dce692">筛选条件</text><text class="filter-close data-v-f3dce692" bindtap="{{w}}">✕</text></view><view class="filter-content data-v-f3dce692"><view class="filter-group data-v-f3dce692"><text class="group-title data-v-f3dce692">活动日期</text><view class="date-options data-v-f3dce692"><view wx:for="{{x}}" wx:for-item="date" wx:key="b" class="{{['date-item', 'data-v-f3dce692', date.c && 'active']}}" bindtap="{{date.d}}">{{date.a}}</view></view></view><view class="filter-group data-v-f3dce692"><text class="group-title data-v-f3dce692">价格范围</text><view class="price-range data-v-f3dce692"><input type="number" placeholder="最低价格" class="price-input data-v-f3dce692" value="{{y}}" bindinput="{{z}}"/><text class="price-separator data-v-f3dce692">-</text><input type="number" placeholder="最高价格" class="price-input data-v-f3dce692" value="{{A}}" bindinput="{{B}}"/></view></view><view class="filter-group data-v-f3dce692"><text class="group-title data-v-f3dce692">参与人数</text><view class="participants-options data-v-f3dce692"><view wx:for="{{C}}" wx:for-item="participants" wx:key="b" class="{{['participants-item', 'data-v-f3dce692', participants.c && 'active']}}" bindtap="{{participants.d}}">{{participants.a}}</view></view></view></view><view class="modal-footer data-v-f3dce692"><button class="reset-btn data-v-f3dce692" bindtap="{{D}}">重置</button><button class="confirm-btn data-v-f3dce692" bindtap="{{E}}">确定</button></view></view></uni-popup><uni-popup wx:if="{{V}}" class="r data-v-f3dce692" u-s="{{['d']}}" u-r="joinPopup" data-c-h="{{!U}}" u-i="f3dce692-2" bind:__l="__l" u-p="{{V}}"><view class="join-modal data-v-f3dce692"><view class="modal-header data-v-f3dce692"><text class="modal-title data-v-f3dce692">申请加入拼场</text><text class="modal-close data-v-f3dce692" bindtap="{{I}}">✕</text></view><view class="modal-content data-v-f3dce692"><view class="form-item data-v-f3dce692"><text class="form-label data-v-f3dce692">队伍名称</text><input class="form-input data-v-f3dce692" placeholder="请输入队伍名称（可选）" maxlength="20" value="{{J}}" bindinput="{{K}}"/></view><view class="form-item data-v-f3dce692"><text class="form-label data-v-f3dce692">联系方式 <text class="required data-v-f3dce692">*</text></text><input class="form-input data-v-f3dce692" placeholder="请输入手机号或微信号" maxlength="50" value="{{L}}" bindinput="{{M}}"/></view><view class="form-item data-v-f3dce692"><text class="form-label data-v-f3dce692">申请说明</text><text class="form-hint data-v-f3dce692">您将代表一支球队申请加入此拼场</text></view><view class="form-item data-v-f3dce692"><text class="form-label data-v-f3dce692">申请留言</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-f3dce692" placeholder="请输入申请留言（可选）" maxlength="200" value="{{N}}" bindinput="{{O}}"></textarea></block><text class="char-count data-v-f3dce692">{{P}}/200</text></view></view><view class="modal-actions data-v-f3dce692"><button class="modal-btn cancel-btn data-v-f3dce692" bindtap="{{Q}}"> 取消 </button><button class="modal-btn confirm-btn data-v-f3dce692" disabled="{{R}}" bindtap="{{S}}"> 提交申请 </button></view></view></uni-popup><view class="floating-btn data-v-f3dce692" bindtap="{{W}}"><text class="floating-btn-text data-v-f3dce692">我的拼场</text></view></view>