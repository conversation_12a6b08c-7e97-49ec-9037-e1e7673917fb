<view class="container data-v-ebed24a8"><view class="header data-v-ebed24a8"><image src="{{a}}" class="logo data-v-ebed24a8" mode="aspectFit"/><text class="app-name data-v-ebed24a8">体育馆预约</text><text class="app-slogan data-v-ebed24a8">让运动更简单</text></view><view class="login-form data-v-ebed24a8"><view class="tab-bar data-v-ebed24a8"><view class="{{['tab-item', 'data-v-ebed24a8', b && 'active']}}" bindtap="{{c}}"> 密码登录 </view><view class="{{['tab-item', 'data-v-ebed24a8', d && 'active']}}" bindtap="{{e}}"> 验证码登录 </view></view><view class="form-item data-v-ebed24a8"><view class="input-wrapper data-v-ebed24a8"><text class="input-icon data-v-ebed24a8">📱</text><input class="input-field data-v-ebed24a8" type="number" placeholder="请输入手机号" maxlength="11" value="{{f}}" bindinput="{{g}}"/></view></view><view wx:if="{{h}}" class="form-item data-v-ebed24a8"><view class="input-wrapper data-v-ebed24a8"><text class="input-icon data-v-ebed24a8">🔒</text><input class="input-field data-v-ebed24a8" password="{{i}}" placeholder="请输入密码" value="{{j}}" bindinput="{{k}}"/><text class="password-toggle data-v-ebed24a8" bindtap="{{m}}">{{l}}</text></view></view><view wx:if="{{n}}" class="form-item data-v-ebed24a8"><view class="input-wrapper data-v-ebed24a8"><text class="input-icon data-v-ebed24a8">💬</text><input class="input-field data-v-ebed24a8" type="number" placeholder="请输入验证码" maxlength="6" value="{{o}}" bindinput="{{p}}"/><button class="sms-btn data-v-ebed24a8" disabled="{{r}}" bindtap="{{s}}">{{q}}</button></view></view><button class="login-btn data-v-ebed24a8" disabled="{{t}}" bindtap="{{v}}"> 登录 </button><view wx:if="{{w}}" class="forgot-password data-v-ebed24a8"><text class="data-v-ebed24a8" bindtap="{{x}}">忘记密码？</text></view></view><view class="other-login data-v-ebed24a8"><view class="divider data-v-ebed24a8"><text class="divider-text data-v-ebed24a8">其他登录方式</text></view><view class="social-login data-v-ebed24a8"><view class="social-item data-v-ebed24a8" bindtap="{{y}}"><text class="social-icon data-v-ebed24a8">💬</text><text class="social-text data-v-ebed24a8">微信登录</text></view><view class="social-item data-v-ebed24a8" bindtap="{{z}}"><text class="social-icon data-v-ebed24a8">🍎</text><text class="social-text data-v-ebed24a8">Apple登录</text></view></view></view><view class="footer data-v-ebed24a8"><text class="footer-text data-v-ebed24a8">还没有账号？</text><text class="footer-link data-v-ebed24a8" bindtap="{{A}}">立即注册</text></view><view class="agreement data-v-ebed24a8"><text class="agreement-text data-v-ebed24a8">登录即表示同意</text><text class="agreement-link data-v-ebed24a8" bindtap="{{B}}">《用户协议》</text><text class="agreement-text data-v-ebed24a8">和</text><text class="agreement-link data-v-ebed24a8" bindtap="{{C}}">《隐私政策》</text></view></view>