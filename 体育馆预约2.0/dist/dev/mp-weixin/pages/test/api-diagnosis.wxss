
.container.data-v-15e82015 {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-15e82015 {
  text-align: center;
  margin-bottom: 60rpx;
}
.title.data-v-15e82015 {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-15e82015 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.test-section.data-v-15e82015 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-15e82015 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.test-btn.data-v-15e82015 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.test-btn.data-v-15e82015:disabled {
  background: #ccc;
}
.results-section.data-v-15e82015 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.log-container.data-v-15e82015 {
  max-height: 800rpx;
  overflow-y: auto;
}
.log-item.data-v-15e82015 {
  padding: 20rpx;
  margin-bottom: 10rpx;
  border-radius: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.log-item.info.data-v-15e82015 {
  background-color: #e3f2fd;
  border-left: 4rpx solid #2196f3;
}
.log-item.success.data-v-15e82015 {
  background-color: #e8f5e8;
  border-left: 4rpx solid #4caf50;
}
.log-item.error.data-v-15e82015 {
  background-color: #ffebee;
  border-left: 4rpx solid #f44336;
}
.log-text.data-v-15e82015 {
  font-size: 26rpx;
  flex: 1;
}
.log-time.data-v-15e82015 {
  font-size: 22rpx;
  color: #999;
  margin-left: 20rpx;
}
