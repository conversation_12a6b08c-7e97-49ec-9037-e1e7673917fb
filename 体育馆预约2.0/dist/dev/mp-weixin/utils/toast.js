"use strict";
const common_vendor = require("../common/vendor.js");
function showError(title, duration = 3e3, mask = false) {
  if (!title) {
    console.warn("[Toast] showError: title参数不能为空");
    return;
  }
  try {
    common_vendor.index.showToast({
      title: String(title),
      icon: "error",
      duration,
      mask
    });
    console.log("[Toast] 显示错误提示:", title);
  } catch (error) {
    console.error("[Toast] showError失败:", error);
    console.log("错误:", title);
  }
}
exports.showError = showError;
