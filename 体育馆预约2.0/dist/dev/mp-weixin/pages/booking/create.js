"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_venue = require("../../stores/venue.js");
const stores_booking = require("../../stores/booking.js");
const stores_user = require("../../stores/user.js");
const api_timeslot = require("../../api/timeslot.js");
require("../../utils/debug-timeslot-expired.js");
const _sfc_main = {
  name: "BookingCreate",
  data() {
    return {
      venueStore: null,
      bookingStore: null,
      userStore: null,
      venueId: null,
      selectedDate: "",
      selectedSlot: null,
      selectedSlots: [],
      // 存储多个选中的时间段
      // 🔒 并发控制
      isConfirmingBooking: false,
      isLoadingData: false,
      // 📊 性能监控
      performanceMetrics: {
        loadStartTime: 0,
        confirmStartTime: 0,
        networkStartTime: 0
      },
      // 🗜️ 数据压缩配置
      compressionConfig: {
        enabled: true,
        minSize: 1e3,
        // 超过1KB才压缩
        level: 6
        // 压缩级别
      },
      bookingForm: {
        bookingType: "EXCLUSIVE",
        teamName: "",
        contactInfo: "",
        description: ""
      },
      // 📱 用户体验优化
      uxState: {
        showLoading: false,
        loadingText: "加载中...",
        lastError: null,
        retryCount: 0
      },
      // 🎯 自定义确认弹窗
      showConfirmDialog: false,
      confirmDialogData: null,
      confirmDialogResolve: null
    };
  },
  computed: {
    timeSlots() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.timeSlots) || [];
    },
    userInfo() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.userInfoGetter) || {};
    },
    // 获取场馆信息，确保有默认值
    venue() {
      var _a, _b;
      const venueData = ((_a = this.venueStore) == null ? void 0 : _a.venueDetailGetter) || ((_b = this.venueStore) == null ? void 0 : _b.venueDetail);
      if (!venueData) {
        return null;
      }
      const result = {
        ...venueData,
        supportSharing: venueData.supportSharing !== void 0 ? venueData.supportSharing : true,
        price: venueData.price || 0
      };
      return result;
    },
    totalCost() {
      var _a, _b;
      if (this.selectedSlots && this.selectedSlots.length > 0) {
        return this.selectedSlots.reduce((sum, slot) => {
          var _a2;
          let slotPrice = 0;
          if (slot.price) {
            slotPrice = parseFloat(slot.price);
          } else if (slot.pricePerHour) {
            slotPrice = parseFloat(slot.pricePerHour);
          } else {
            const venuePrice2 = ((_a2 = this.venue) == null ? void 0 : _a2.price) || 0;
            slotPrice = parseFloat(venuePrice2) / 2 || 0;
          }
          return sum + slotPrice;
        }, 0);
      }
      if (this.selectedSlot && this.selectedSlot.price) {
        return parseFloat(this.selectedSlot.price);
      }
      if (this.selectedSlot && this.selectedSlot.pricePerHour) {
        return parseFloat(this.selectedSlot.pricePerHour);
      }
      const venuePrice = ((_a = this.venue) == null ? void 0 : _a.pricePerHour) || ((_b = this.venue) == null ? void 0 : _b.price) || 0;
      return parseFloat(venuePrice) || 0;
    },
    // 🔥 修复：添加缺失的isMultiSlot计算属性
    isMultiSlot() {
      return this.selectedSlots && this.selectedSlots.length > 1;
    },
    canConfirm() {
      var _a, _b, _c, _d, _e;
      const hasDate = !!this.selectedDate;
      const hasSlot = !!(((_a = this.selectedSlots) == null ? void 0 : _a.length) > 0 || this.selectedSlot);
      const hasVenue = !!((_b = this.venue) == null ? void 0 : _b.id);
      const hasPrice = !!((_c = this.venue) == null ? void 0 : _c.price);
      const baseValid = hasDate && hasSlot && hasVenue && hasPrice;
      console.log("canConfirm 详细检查:", {
        selectedDate: this.selectedDate,
        hasDate,
        selectedSlot: this.selectedSlot,
        hasSlot,
        venue: this.venue,
        hasVenue,
        hasPrice,
        venuePrice: (_d = this.venue) == null ? void 0 : _d.price,
        venuePricePerHour: (_e = this.venue) == null ? void 0 : _e.price,
        bookingType: this.bookingForm.bookingType,
        teamName: this.bookingForm.teamName,
        contactInfo: this.bookingForm.contactInfo,
        baseValid
      });
      if (this.bookingForm.bookingType === "SHARED") {
        const hasTeamName = !!(this.bookingForm.teamName && this.bookingForm.teamName.trim());
        const hasContactInfo = !!(this.bookingForm.contactInfo && this.bookingForm.contactInfo.trim());
        const result = baseValid && hasTeamName && hasContactInfo;
        console.log("拼场模式额外检查:", {
          hasTeamName,
          hasContactInfo,
          finalResult: result
        });
        return result;
      }
      console.log("独占模式 canConfirm 结果:", baseValid);
      return baseValid;
    }
  },
  onLoad(options) {
    this.venueStore = stores_venue.useVenueStore();
    this.bookingStore = stores_booking.useBookingStore();
    this.userStore = stores_user.useUserStore();
    console.log("[BookingCreate] 📋 页面加载参数:", options);
    this.venueId = options.venueId;
    this.selectedDate = options.date;
    console.error("[BookingCreate] 🚨 强制调试 - 接收到的参数:", {
      venueId: options.venueId,
      date: options.date,
      bookingType: options.bookingType,
      hasSelectedSlots: !!options.selectedSlots,
      selectedSlotsLength: options.selectedSlots ? options.selectedSlots.length : 0
    });
    if (options.bookingType) {
      this.bookingForm.bookingType = options.bookingType;
      console.log("[BookingCreate] 🎯 设置预约类型:", this.bookingForm.bookingType);
    }
    if (options.selectedSlots) {
      try {
        this.selectedSlots = JSON.parse(decodeURIComponent(options.selectedSlots));
        console.log("[BookingCreate] 📦 接收到的时间段数据:", this.selectedSlots);
        console.log("[BookingCreate] 🎯 多时间段预约，时间段数量:", this.selectedSlots.length);
        if (this.selectedSlots.length > 0 && this.selectedSlots[0].date) {
          const slotDate = this.selectedSlots[0].date;
          console.log("[BookingCreate] 🚨 日期修复:", {
            原始日期: this.selectedDate,
            时间段日期: slotDate,
            是否需要修复: this.selectedDate !== slotDate
          });
          if (this.selectedDate !== slotDate) {
            console.warn("[BookingCreate] ⚠️ 检测到日期不一致，使用时间段的日期:", slotDate);
            this.selectedDate = slotDate;
          }
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 解析时间段数据失败:", error);
      }
    }
  },
  onShow() {
    console.log("[BookingCreate] 🔄 onShow: 开始优化的数据刷新流程");
    this.setupEventListeners();
    if (this.venueId) {
      this.loadPageDataOptimized();
      setTimeout(() => {
        console.log("[BookingCreate] 🔄 onShow: 额外强制刷新时间段状态");
        this.refreshTimeSlotStatusSafe(true);
        if (this.bookingForm.bookingType === "SHARED") {
          console.log("[BookingCreate] 🎯 拼场预约页面，执行深度刷新");
          if (this.venueStore && this.venueStore.cache && this.venueStore.cache.timeSlots) {
            const cacheKey = `${this.venueId}_${this.selectedDate}`;
            this.venueStore.cache.timeSlots.delete(cacheKey);
          }
          setTimeout(() => {
            this.refreshTimeSlotStatusSafe(true);
          }, 300);
        }
      }, 500);
    }
  },
  onHide() {
    console.log("[BookingCreate] 🧹 onHide: 清理页面状态");
    this.removeEventListeners();
  },
  methods: {
    // 🚀 优化的并行数据加载方法
    async loadPageDataOptimized() {
      try {
        this.recordPerformanceMetric("page-load-start", {
          venueId: this.venueId,
          selectedDate: this.selectedDate
        });
        console.log("[BookingCreate] 🚀 开始优化的并行数据加载");
        this.showUserFeedback("loading", "正在加载页面数据...");
        this.isLoadingData = true;
        const cacheKey = `booking_page_data_${this.venueId}_${this.selectedDate}`;
        const cachedData = common_vendor.index.getStorageSync(cacheKey);
        const now = Date.now();
        if (cachedData && cachedData.timestamp && now - cachedData.timestamp < 3 * 60 * 1e3) {
          this.recordPerformanceMetric("cache-hit", {
            cacheKey,
            cacheAge: now - cachedData.timestamp
          });
          console.log("[BookingCreate] 🎯 使用缓存数据，缓存时间:", (now - cachedData.timestamp) / 1e3, "秒");
          if (cachedData.venue && this.venueStore) {
            this.venueStore.setVenueDetail(cachedData.venue);
          }
          if (cachedData.timeSlots && this.venueStore) {
            this.venueStore.setTimeSlots(cachedData.timeSlots);
          }
          this.showUserFeedback("hide-loading");
          this.showUserFeedback("success", "页面加载完成");
          this.isLoadingData = false;
          this.recordPerformanceMetric("page-load-complete", { source: "cache" });
          return;
        }
        this.recordPerformanceMetric("network-request-start");
        const tasks = [];
        tasks.push(this.loadVenueDetailSafe());
        if (this.selectedDate) {
          tasks.push(this.refreshTimeSlotStatusSafe());
        }
        const results = await Promise.allSettled(tasks);
        this.recordPerformanceMetric("network-request-complete");
        let successCount = 0;
        results.forEach((result, index) => {
          var _a;
          if (result.status === "rejected") {
            console.warn(`[BookingCreate] ⚠️ 任务 ${index + 1} 执行失败:`, result.reason);
            this.recordPerformanceMetric("task-error", {
              taskIndex: index + 1,
              error: (_a = result.reason) == null ? void 0 : _a.message
            });
          } else {
            console.log(`[BookingCreate] ✅ 任务 ${index + 1} 执行成功`);
            successCount++;
          }
        });
        if (successCount > 0) {
          const cacheData = {
            venue: this.venue,
            timeSlots: this.timeSlots,
            timestamp: now
          };
          try {
            common_vendor.index.setStorageSync(cacheKey, cacheData);
            console.log("[BookingCreate] 💾 页面数据已缓存");
          } catch (cacheError) {
            console.warn("[BookingCreate] ⚠️ 缓存存储失败:", cacheError);
          }
        }
        setTimeout(() => {
          this.validateBookingData();
        }, 300);
        console.log("[BookingCreate] 🎉 优化的数据加载流程完成");
        this.showUserFeedback("hide-loading");
        if (successCount === results.length) {
          this.showUserFeedback("success", "页面加载完成");
        } else if (successCount > 0) {
          this.showUserFeedback("success", "页面部分加载完成");
        } else {
          this.showUserFeedback("error", "页面加载失败");
        }
        this.recordPerformanceMetric("page-load-complete", {
          source: "network",
          successCount,
          totalTasks: results.length
        });
      } catch (error) {
        console.error("[BookingCreate] ❌ 优化数据加载失败:", error);
        this.recordPerformanceMetric("page-load-error", {
          error: error.message,
          stack: error.stack
        });
        this.showUserFeedback("hide-loading");
        this.showUserFeedback("error", "页面加载失败，请重试");
      } finally {
        this.isLoadingData = false;
      }
    },
    // 🛡️ 安全的场馆详情加载方法
    async loadVenueDetailSafe() {
      try {
        console.log("[BookingCreate] 📍 安全加载场馆详情");
        await this.loadVenueDetail();
      } catch (error) {
        console.error("[BookingCreate] ❌ 场馆详情加载失败:", error);
        throw error;
      }
    },
    // 🛡️ 安全的时间段状态刷新方法
    async refreshTimeSlotStatusSafe(forceRefresh = false) {
      try {
        console.log("[BookingCreate] 🕒 安全刷新时间段状态, forceRefresh:", forceRefresh);
        if (forceRefresh) {
          console.log("[BookingCreate] 🔄 强制刷新模式：清除缓存");
          if (this.venueStore && typeof this.venueStore.forceRefreshTimeSlots === "function") {
            await this.venueStore.forceRefreshTimeSlots(this.venueId, this.selectedDate);
          } else {
            await this.refreshTimeSlotStatus();
          }
        } else {
          await this.refreshTimeSlotStatus();
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 时间段状态刷新失败:", error);
        throw error;
      }
    },
    // 🔄 刷新时间段状态
    async refreshTimeSlotStatus() {
      try {
        console.log("[BookingCreate] 🔄 开始刷新时间段状态");
        if (this.venueId && this.selectedDate) {
          await this.venueStore.refreshTimeSlotStatus(this.venueId, this.selectedDate);
          console.log("[BookingCreate] ✅ 时间段状态刷新完成");
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 刷新时间段状态失败:", error);
        throw error;
      }
    },
    // 🔍 验证预约数据一致性
    validateBookingData() {
      console.log("[BookingCreate] 🔍 开始验证预约数据一致性");
      if (this.bookingForm.bookingType) {
        console.log("[BookingCreate] 📋 当前预约类型:", this.bookingForm.bookingType);
        if (this.bookingForm.bookingType === "SHARED" || this.bookingForm.bookingType === "SHARING") {
          console.log("[BookingCreate] ✅ 确认为拼场预约");
        } else if (this.bookingForm.bookingType === "EXCLUSIVE") {
          console.log("[BookingCreate] ✅ 确认为独享预约");
        } else {
          console.warn("[BookingCreate] ⚠️ 预约类型异常:", this.bookingForm.bookingType);
        }
      } else {
        console.warn("[BookingCreate] ⚠️ 预约类型未设置");
      }
      const selectedSlots = this.selectedSlots || this.bookingForm.selectedSlots || [];
      if (selectedSlots.length > 0) {
        console.log("[BookingCreate] 📋 选中的时间段:", selectedSlots);
        selectedSlots.forEach((slot) => {
          const currentSlot = this.timeSlots.find((ts) => ts.id === slot.id);
          if (currentSlot) {
            if (currentSlot.status !== "AVAILABLE") {
              console.warn("[BookingCreate] ⚠️ 时间段状态已变更:", {
                slotId: slot.id,
                originalStatus: slot.status,
                currentStatus: currentSlot.status
              });
            }
          } else {
            console.warn("[BookingCreate] ⚠️ 时间段不存在:", slot);
          }
        });
      } else {
        console.warn("[BookingCreate] ⚠️ 未选择时间段");
      }
      console.log("[BookingCreate] ✅ 数据验证完成");
    },
    // 加载场馆详情
    async loadVenueDetail() {
      var _a;
      try {
        this.recordPerformanceMetric("venue-detail-start", { venueId: this.venueId });
        console.log("[BookingCreate] 🏟️ 开始加载场馆详情，venueId:", this.venueId);
        const cacheKey = `venue_detail_${this.venueId}`;
        const cachedVenue = common_vendor.index.getStorageSync(cacheKey);
        const now = Date.now();
        if (cachedVenue && cachedVenue.timestamp && now - cachedVenue.timestamp < 5 * 60 * 1e3) {
          this.recordPerformanceMetric("venue-cache-hit", {
            venueId: this.venueId,
            cacheAge: now - cachedVenue.timestamp
          });
          console.log("[BookingCreate] 🎯 使用缓存的场馆详情，缓存时间:", (now - cachedVenue.timestamp) / 1e3, "秒");
          this.venueStore.setVenueDetail(cachedVenue.data);
        } else {
          const requestParams = {
            venueId: parseInt(this.venueId),
            compress: this.compressionConfig.enabled,
            fields: this.compressionConfig.optimizeFields ? "id,name,price,supportSharing,location,openingHours,status" : void 0
          };
          await this.venueStore.getVenueDetail(this.venueId, requestParams);
          try {
            const cacheData = {
              data: this.venue,
              timestamp: now
            };
            common_vendor.index.setStorageSync(cacheKey, cacheData);
            console.log("[BookingCreate] 💾 场馆详情已缓存");
          } catch (cacheError) {
            console.warn("[BookingCreate] ⚠️ 场馆详情缓存失败:", cacheError);
          }
        }
        console.log("[BookingCreate] ✅ 场馆详情加载完成，数据:", this.venueStore.venueDetailGetter);
        if (this.selectedDate) {
          await this.loadTimeSlots();
        }
        this.recordPerformanceMetric("venue-detail-success", {
          venueId: this.venueId,
          venueName: (_a = this.venue) == null ? void 0 : _a.name
        });
      } catch (error) {
        console.error("[BookingCreate] ❌ 加载场馆详情失败:", error);
        this.recordPerformanceMetric("venue-detail-error", {
          venueId: this.venueId,
          error: error.message
        });
        console.log("[BookingCreate] 🔧 设置模拟场馆数据用于测试");
        const mockVenueData = {
          id: this.venueId || 1,
          name: "测试体育馆",
          price: 120,
          supportSharing: true,
          location: "测试地址",
          openingHours: "08:00 - 22:00"
        };
        this.venueStore.setVenueDetail(mockVenueData);
        if (this.selectedDate) {
          const mockTimeSlots = [
            {
              id: 1,
              startTime: "09:00",
              endTime: "10:00",
              status: "AVAILABLE",
              price: 120
            },
            {
              id: 2,
              startTime: "10:00",
              endTime: "11:00",
              status: "AVAILABLE",
              price: 120
            }
          ];
          this.venueStore.setTimeSlots(mockTimeSlots);
        }
        this.showUserFeedback("error", "场馆信息加载失败，使用测试数据");
        this.recordPerformanceMetric("venue-mock-data", { venueId: this.venueId });
      }
    },
    // 加载场馆和指定时间段
    async loadVenueAndSlot(slotId) {
      try {
        console.log("loadVenueAndSlot 开始，slotId:", slotId);
        await this.venueStore.getVenueDetail(this.venueId);
        await this.loadTimeSlots();
        console.log("可用时间段:", this.timeSlots);
        let slot = this.timeSlots.find((s) => s.id == slotId);
        if (!slot && slotId.includes("-")) {
          const [startTime, endTime] = slotId.split("-");
          slot = this.timeSlots.find((s) => s.startTime === startTime && s.endTime === endTime);
        }
        console.log("找到的时间段:", slot);
        if (slot) {
          this.selectedSlot = slot;
          console.log("已设置 selectedSlot:", this.selectedSlot);
        } else {
          console.warn("未找到指定的时间段:", slotId);
        }
      } catch (error) {
        console.error("加载失败:", error);
        console.log("设置模拟数据用于测试");
        this.venueStore.setVenueDetail({
          id: this.venueId || 1,
          name: "测试体育馆",
          price: 120,
          supportSharing: true,
          location: "测试地址",
          openingHours: "08:00 - 22:00"
        });
        const mockSlots = [
          {
            id: 1,
            startTime: "09:00",
            endTime: "10:00",
            status: "AVAILABLE",
            price: 120
          },
          {
            id: 2,
            startTime: "10:00",
            endTime: "11:00",
            status: "AVAILABLE",
            price: 120
          },
          {
            id: 3,
            startTime: "14:00",
            endTime: "15:00",
            status: "AVAILABLE",
            price: 120
          }
        ];
        this.venueStore.setTimeSlots(mockSlots);
        if (slotId) {
          let slot = mockSlots.find((s) => s.id == slotId);
          if (!slot && slotId.includes("-")) {
            const [startTime, endTime] = slotId.split("-");
            slot = mockSlots.find((s) => s.startTime === startTime && s.endTime === endTime);
          }
          if (slot) {
            this.selectedSlot = slot;
            console.log("已设置模拟 selectedSlot:", this.selectedSlot);
          }
        }
        common_vendor.index.showToast({
          title: "使用模拟数据",
          icon: "none"
        });
      }
    },
    // 加载时间段
    async loadTimeSlots() {
      if (!this.selectedDate) {
        console.warn("[BookingCreate] 缺少选择日期，无法加载时间段");
        return;
      }
      console.log("[BookingCreate] 开始加载时间段:", { venueId: this.venueId, date: this.selectedDate });
      try {
        common_vendor.index.showLoading({ title: "加载时间段..." });
        const result = await this.venueStore.getVenueTimeSlots({
          venueId: this.venueId,
          date: this.selectedDate,
          loading: false
          // 🔥 修复：禁用API请求的自动loading，避免与手动loading冲突
        });
        console.log("[BookingCreate] 时间段加载结果:", result);
        const timeSlots = this.timeSlots || [];
        console.log("[BookingCreate] 当前时间段数量:", timeSlots.length);
        if (timeSlots.length === 0) {
          console.warn("[BookingCreate] 未获取到时间段数据，该日期暂无可预约时间段");
          common_vendor.index.showToast({
            title: "该日期暂无可预约时间段",
            icon: "none",
            duration: 2e3
          });
        }
        console.log("[BookingCreate] 时间段加载完成");
      } catch (error) {
        console.error("[BookingCreate] 加载时间段失败:", error);
        common_vendor.index.showToast({
          title: "加载时间段失败，请重试",
          icon: "none",
          duration: 2e3
        });
        console.error("[BookingCreate] 时间段加载失败，请手动刷新或联系管理员");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 格式化日期时间
    formatDateTime(date, slot) {
      console.log("formatDateTime 调用:", { date, slot, selectedSlots: this.selectedSlots });
      if (!date) {
        console.log("formatDateTime 返回默认值: 请选择时间");
        return "请选择时间";
      }
      try {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = dateObj.getMonth() + 1;
        const day = dateObj.getDate();
        const weekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][dateObj.getDay()];
        const dateStr = `${year}年${month}月${day}日 ${weekDay}`;
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const sortedSlots = [...this.selectedSlots].sort((a, b) => {
            const timeA = a.startTime.split(":").map(Number);
            const timeB = b.startTime.split(":").map(Number);
            if (timeA[0] !== timeB[0]) {
              return timeA[0] - timeB[0];
            }
            return timeA[1] - timeB[1];
          });
          const timeSlots = sortedSlots.map((slot2) => {
            let startTime3 = slot2.startTime;
            let endTime3 = slot2.endTime;
            if (startTime3 && startTime3.length > 5) {
              startTime3 = startTime3.substring(0, 5);
            }
            if (endTime3 && endTime3.length > 5) {
              endTime3 = endTime3.substring(0, 5);
            }
            return `${startTime3}-${endTime3}`;
          });
          const totalDuration = sortedSlots.reduce((total, slot2) => {
            return total + this.calculateDuration(slot2.startTime, slot2.endTime);
          }, 0);
          const durationText2 = totalDuration % 1 === 0 ? totalDuration : totalDuration.toFixed(1);
          const firstSlot = sortedSlots[0];
          const lastSlot = sortedSlots[sortedSlots.length - 1];
          let startTime2 = firstSlot.startTime.length > 5 ? firstSlot.startTime.substring(0, 5) : firstSlot.startTime;
          let endTime2 = lastSlot.endTime.length > 5 ? lastSlot.endTime.substring(0, 5) : lastSlot.endTime;
          const result2 = `${dateStr} ${startTime2}-${endTime2} (共${durationText2}小时，${sortedSlots.length}个时间段)`;
          console.log("formatDateTime 多时间段结果:", result2);
          console.log("📅 时间段详情:", timeSlots.join("、"));
          return result2;
        }
        if (!slot) {
          console.log("formatDateTime 返回默认值: 请选择时间");
          return "请选择时间";
        }
        let startTime = slot.startTime;
        let endTime = slot.endTime;
        if (startTime && startTime.length > 5) {
          startTime = startTime.substring(0, 5);
        }
        if (endTime && endTime.length > 5) {
          endTime = endTime.substring(0, 5);
        }
        const duration = this.calculateDuration(startTime, endTime);
        const durationText = duration % 1 === 0 ? duration : duration.toFixed(1);
        const timeStr = `${startTime}-${endTime}`;
        const result = `${dateStr} ${timeStr} (${durationText}小时)`;
        console.log("formatDateTime 单时间段结果:", result);
        return result;
      } catch (error) {
        console.error("formatDateTime 错误:", error);
        return "时间格式错误";
      }
    },
    // 计算时长
    calculateDuration(startTime, endTime) {
      try {
        const [startHour, startMinute] = startTime.split(":").map(Number);
        const [endHour, endMinute] = endTime.split(":").map(Number);
        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;
        const durationMinutes = endMinutes - startMinutes;
        const hours = durationMinutes / 60;
        return Math.round(hours * 10) / 10;
      } catch (error) {
        console.error("计算时长错误:", error);
        return 1;
      }
    },
    // 获取单个时间段的价格
    getSlotPrice(slot) {
      var _a;
      if (slot.price && slot.price > 0) {
        return parseFloat(slot.price);
      }
      if (slot.pricePerHour && slot.pricePerHour > 0) {
        return parseFloat(slot.pricePerHour);
      }
      const venuePrice = ((_a = this.venue) == null ? void 0 : _a.price) || 0;
      if (venuePrice > 0) {
        return parseFloat(venuePrice) / 2;
      }
      return 60;
    },
    // 获取时间段状态文本
    getSlotStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "RESERVED": "已预约",
        "OCCUPIED": "已占用",
        "MAINTENANCE": "维护中",
        "BOOKED": "已预订",
        "SHARING": "拼场中",
        "EXPIRED": "已过期"
      };
      return statusMap[status] || status;
    },
    // 🚀 优化的确认预约方法
    async confirmBooking() {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;
      if (this.isConfirmingBooking) {
        console.warn("[BookingCreate] ⚠️ 预约确认正在进行中，忽略重复请求");
        return;
      }
      console.log("[BookingCreate] 🚀 开始预约确认流程");
      console.log("[BookingCreate] 📋 基础状态检查:", {
        canConfirm: this.canConfirm,
        selectedSlots: ((_a = this.selectedSlots) == null ? void 0 : _a.length) || 0,
        selectedSlot: !!this.selectedSlot,
        bookingType: this.bookingForm.bookingType
      });
      if (!this.canConfirm) {
        console.warn("[BookingCreate] ❌ 无法确认预约，canConfirm为false");
        return;
      }
      try {
        const confirmResult = await this.showConfirmationDialog();
        if (!confirmResult) {
          console.log("[BookingCreate] 👤 用户取消预约确认");
          return;
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 确认对话框异常:", error);
        return;
      }
      console.log("[BookingCreate] 🔄 开始实时时间段状态验证");
      try {
        const realtimeValidationResult = await this.validateTimeSlotsRealtime();
        if (!realtimeValidationResult) {
          console.warn("[BookingCreate] ❌ 实时状态验证失败");
          this.showUserFeedback("error", "时间段状态验证失败，请重新选择");
          this.recordPerformanceMetric("realtime-validation-failed", {
            reason: "validation_failed"
          });
          return;
        }
        console.log("[BookingCreate] ✅ 实时状态验证通过");
      } catch (error) {
        console.error("[BookingCreate] ❌ 实时状态验证异常:", error);
        this.showUserFeedback("error", "无法验证时间段状态，请稍后重试");
        return;
      }
      if (!this.validateForm()) {
        console.warn("[BookingCreate] ❌ 表单验证失败");
        return;
      }
      this.isConfirmingBooking = true;
      try {
        common_vendor.index.showLoading({ title: "创建中..." });
        console.log("[BookingCreate] 📊 性能监控 - 预约确认开始");
        let result;
        const compressedData = this.compressBookingData();
        console.log("[BookingCreate] 🗜️ 数据压缩完成，压缩后大小:", JSON.stringify(compressedData).length);
        console.log("[BookingCreate] 🔍 检查时间段选择:");
        console.log("- selectedSlots:", ((_b = this.selectedSlots) == null ? void 0 : _b.length) || 0);
        console.log("- selectedSlot:", !!this.selectedSlot);
        console.log("- 使用多时间段:", this.selectedSlots && this.selectedSlots.length > 0);
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const sortedSlots = [...this.selectedSlots].sort((a, b) => {
            const timeA = a.startTime.split(":").map(Number);
            const timeB = b.startTime.split(":").map(Number);
            if (timeA[0] !== timeB[0]) {
              return timeA[0] - timeB[0];
            }
            return timeA[1] - timeB[1];
          });
          console.log("📅 排序前的时间段:", this.selectedSlots.map((s) => `${s.startTime}-${s.endTime}`));
          console.log("📅 排序后的时间段:", sortedSlots.map((s) => `${s.startTime}-${s.endTime}`));
          const firstSlot = sortedSlots[0];
          const lastSlot = sortedSlots[sortedSlots.length - 1];
          if (this.bookingForm.bookingType === "SHARED") {
            const totalPrice = sortedSlots.reduce((total, slot) => {
              return total + this.getSlotPrice(slot);
            }, 0);
            const pricePerTeam = Math.round(totalPrice / 2 * 100) / 100;
            console.log(`💰 [前端价格计算] 多时间段拼场预约:`);
            console.log(`   - 选中时间段数量: ${sortedSlots.length}`);
            console.log(`   - 实际开始时间: ${firstSlot.startTime}`);
            console.log(`   - 实际结束时间: ${lastSlot.endTime}`);
            console.log(`   - 场地总价格: ¥${totalPrice}`);
            console.log(`   - 每队支付价格: ¥${pricePerTeam}`);
            const actualDate = firstSlot.date || this.selectedDate;
            console.log("[CreatePage] 🚨 多时间段拼场预约日期修复:", {
              原始selectedDate: this.selectedDate,
              时间段实际日期: firstSlot.date,
              最终使用日期: actualDate
            });
            const sharedBookingData = {
              venueId: parseInt(this.venueId),
              date: actualDate,
              // 使用时间段的实际日期
              startTime: firstSlot.startTime,
              // 最早时间段的开始时间
              endTime: lastSlot.endTime,
              // 最晚时间段的结束时间
              teamName: this.bookingForm.teamName || "",
              contactInfo: this.bookingForm.contactInfo || "",
              maxParticipants: 2,
              // 拼场固定2队
              description: this.bookingForm.description || "",
              slotIds: sortedSlots.map((slot) => slot.id),
              // 按时间排序的时间段ID
              price: pricePerTeam
              // 🔑 关键：传递每队价格给后端
            };
            console.log(`📤 [发送数据] 多时间段拼场预约数据:`, sharedBookingData);
            result = await this.bookingStore.createSharedBooking(sharedBookingData);
          } else {
            const totalPrice = sortedSlots.reduce((total, slot) => {
              return total + this.getSlotPrice(slot);
            }, 0);
            const multiSlotBookingData = {
              venueId: parseInt(this.venueId),
              date: this.selectedDate,
              startTime: firstSlot.startTime,
              endTime: lastSlot.endTime,
              slotIds: sortedSlots.map((slot) => slot.id),
              bookingType: this.bookingForm.bookingType,
              description: this.bookingForm.description || "",
              price: totalPrice || sortedSlots.length * 60,
              // 备用价格
              // 🔧 修复：添加fieldName字段确保数据库字段统一
              fieldName: ((_c = this.venue) == null ? void 0 : _c.name) || ((_d = this.venue) == null ? void 0 : _d.fieldName) || "主场地"
            };
            result = await this.bookingStore.createBooking(multiSlotBookingData);
          }
          console.log("预约创建结果:", result);
        } else {
          console.log("🎯 开始创建单时间段预约");
          console.log("[CreatePage] 🎯 构造包场预约数据");
          console.log("[CreatePage] 📊 selectedSlots:", this.selectedSlots);
          console.log("[CreatePage] 📊 selectedSlot:", this.selectedSlot);
          let bookingData;
          if (this.selectedSlots && this.selectedSlots.length > 0) {
            const sortedSlots = [...this.selectedSlots].sort((a, b) => {
              const timeA = a.startTime.split(":").map(Number);
              const timeB = b.startTime.split(":").map(Number);
              if (timeA[0] !== timeB[0])
                return timeA[0] - timeB[0];
              return timeA[1] - timeB[1];
            });
            const firstSlot = sortedSlots[0];
            const lastSlot = sortedSlots[sortedSlots.length - 1];
            const totalPrice = sortedSlots.reduce((sum, slot) => sum + this.getSlotPrice(slot), 0);
            console.log("[CreatePage] 🆔 多时间段IDs:", sortedSlots.map((slot) => slot.id));
            console.log("[CreatePage] ⏰ 时间范围:", firstSlot.startTime, "-", lastSlot.endTime);
            console.log("[CreatePage] 💰 总价格:", totalPrice);
            const actualDate = firstSlot.date || this.selectedDate;
            console.log("[CreatePage] 🚨 日期修复检查:", {
              原始selectedDate: this.selectedDate,
              时间段实际日期: firstSlot.date,
              最终使用日期: actualDate
            });
            bookingData = {
              venueId: parseInt(this.venueId),
              date: actualDate,
              // 使用时间段的实际日期
              startTime: firstSlot.startTime,
              endTime: lastSlot.endTime,
              slotId: firstSlot.id,
              // 主要时间段ID
              slotIds: sortedSlots.map((slot) => slot.id),
              // 所有时间段ID
              bookingType: this.bookingForm.bookingType,
              description: this.bookingForm.description || "",
              price: totalPrice,
              fieldName: ((_e = this.venueDetail) == null ? void 0 : _e.name) || ((_f = this.venueDetail) == null ? void 0 : _f.fieldName) || "主场地"
            };
          } else if (this.selectedSlot) {
            console.log("[CreatePage] 🆔 单时间段ID:", this.selectedSlot.id);
            console.log("[CreatePage] ⏰ 时间范围:", this.selectedSlot.startTime, "-", this.selectedSlot.endTime);
            const actualDate = this.selectedSlot.date || this.selectedDate;
            console.log("[CreatePage] 🚨 单时间段日期修复检查:", {
              原始selectedDate: this.selectedDate,
              时间段实际日期: this.selectedSlot.date,
              最终使用日期: actualDate
            });
            bookingData = {
              venueId: parseInt(this.venueId),
              date: actualDate,
              // 使用时间段的实际日期
              startTime: this.selectedSlot.startTime,
              endTime: this.selectedSlot.endTime,
              slotId: this.selectedSlot.id,
              bookingType: this.bookingForm.bookingType,
              description: this.bookingForm.description || "",
              price: this.getSlotPrice(this.selectedSlot),
              fieldName: ((_g = this.venueDetail) == null ? void 0 : _g.name) || ((_h = this.venueDetail) == null ? void 0 : _h.fieldName) || "主场地"
            };
          } else {
            throw new Error("没有选择时间段");
          }
          console.log("[CreatePage] 📋 构造的包场预约数据:", bookingData);
          console.log("[CreatePage] 🆔 最终slotId:", bookingData.slotId);
          console.log("[CreatePage] 🆔 最终slotId类型:", typeof bookingData.slotId);
          if (!bookingData.venueId || !bookingData.date || !bookingData.startTime || !bookingData.price) {
            throw new Error("预约数据不完整");
          }
          try {
            if (this.bookingForm.bookingType === "SHARED") {
              const pricePerTeam = Math.round(bookingData.price / 2 * 100) / 100;
              console.log("[CreatePage] 🎯 构造拼场预约数据");
              console.log("[CreatePage] 📊 使用的预约数据:", bookingData);
              const actualDate = bookingData.date;
              console.log("[CreatePage] 🚨 拼场预约日期修复:", {
                原始selectedDate: this.selectedDate,
                bookingData中的日期: bookingData.date,
                最终使用日期: actualDate
              });
              const sharedBookingData = {
                venueId: parseInt(this.venueId),
                date: actualDate,
                // 使用时间段的实际日期
                startTime: bookingData.startTime,
                // 使用已计算好的开始时间
                endTime: bookingData.endTime,
                // 使用已计算好的结束时间
                teamName: this.bookingForm.teamName || "",
                contactInfo: this.bookingForm.contactInfo || "",
                maxParticipants: 2,
                description: this.bookingForm.description || "",
                price: pricePerTeam,
                slotIds: bookingData.slotIds || [bookingData.slotId],
                // 使用所有时间段ID
                fieldName: ((_i = this.venueDetail) == null ? void 0 : _i.name) || ((_j = this.venueDetail) == null ? void 0 : _j.fieldName) || "主场地"
              };
              console.log("[CreatePage] 📋 构造的拼场预约数据:", sharedBookingData);
              console.log("[CreatePage] 🆔 最终slotIds:", sharedBookingData.slotIds);
              console.log("[CreatePage] ⏰ 时间范围:", sharedBookingData.startTime, "-", sharedBookingData.endTime);
              result = await this.bookingStore.createSharedBooking(sharedBookingData);
            } else {
              result = await this.bookingStore.createBooking(bookingData);
              console.log("单时间段预约创建结果:", result);
            }
          } catch (error) {
            console.error("预约创建失败:", error);
            throw error;
          }
        }
        try {
          console.log("🔄 开始使用增强的预约成功状态同步");
          let syncData;
          if (this.bookingForm.bookingType === "SHARED") {
            const sortedSlots = [...this.selectedSlots].sort((a, b) => {
              const timeA = a.startTime.split(":").map(Number);
              const timeB = b.startTime.split(":").map(Number);
              if (timeA[0] !== timeB[0])
                return timeA[0] - timeB[0];
              return timeA[1] - timeB[1];
            });
            const firstSlot = sortedSlots[0];
            const lastSlot = sortedSlots[sortedSlots.length - 1];
            const actualDate = firstSlot.date || this.selectedDate;
            syncData = {
              venueId: parseInt(this.venueId),
              venue_id: parseInt(this.venueId),
              date: actualDate,
              // 使用修复后的日期
              booking_date: actualDate,
              bookingType: this.bookingForm.bookingType,
              booking_type: this.bookingForm.bookingType,
              slotIds: sortedSlots.map((slot) => slot.id),
              startTime: firstSlot.startTime,
              endTime: lastSlot.endTime
            };
          } else {
            syncData = {
              venueId: parseInt(this.venueId),
              venue_id: parseInt(this.venueId),
              date: this.selectedDate,
              // 使用页面的选中日期
              booking_date: this.selectedDate,
              bookingType: this.bookingForm.bookingType,
              booking_type: this.bookingForm.bookingType,
              slotIds: this.selectedSlots.length > 0 ? this.selectedSlots.map((slot) => slot.id) : [(_k = this.selectedSlot) == null ? void 0 : _k.id],
              startTime: this.selectedSlots.length > 0 ? this.selectedSlots[0].startTime : (_l = this.selectedSlot) == null ? void 0 : _l.startTime,
              endTime: this.selectedSlots.length > 0 ? this.selectedSlots[this.selectedSlots.length - 1].endTime : (_m = this.selectedSlot) == null ? void 0 : _m.endTime
            };
          }
          const bookingSuccessData = syncData;
          if (this.selectedSlots && this.selectedSlots.length > 0) {
            const sortedSlots = [...this.selectedSlots].sort((a, b) => {
              const timeA = a.startTime.split(":").map(Number);
              const timeB = b.startTime.split(":").map(Number);
              if (timeA[0] !== timeB[0])
                return timeA[0] - timeB[0];
              return timeA[1] - timeB[1];
            });
            const firstSlot = sortedSlots[0];
            const lastSlot = sortedSlots[sortedSlots.length - 1];
            bookingSuccessData.startTime = firstSlot.startTime;
            bookingSuccessData.start_time = firstSlot.startTime;
            bookingSuccessData.bookingStartTime = firstSlot.startTime;
            bookingSuccessData.endTime = lastSlot.endTime;
            bookingSuccessData.end_time = lastSlot.endTime;
            bookingSuccessData.bookingEndTime = lastSlot.endTime;
            bookingSuccessData.timeSlotIds = sortedSlots.map((slot) => slot.id);
            bookingSuccessData.time_slot_ids = sortedSlots.map((slot) => slot.id);
            bookingSuccessData.slotIds = sortedSlots.map((slot) => slot.id);
            if (sortedSlots.length === 1) {
              bookingSuccessData.timeSlotId = sortedSlots[0].id;
              bookingSuccessData.time_slot_id = sortedSlots[0].id;
              bookingSuccessData.slotId = sortedSlots[0].id;
            }
          } else if (this.selectedSlot) {
            bookingSuccessData.startTime = this.selectedSlot.startTime;
            bookingSuccessData.start_time = this.selectedSlot.startTime;
            bookingSuccessData.bookingStartTime = this.selectedSlot.startTime;
            bookingSuccessData.endTime = this.selectedSlot.endTime;
            bookingSuccessData.end_time = this.selectedSlot.endTime;
            bookingSuccessData.bookingEndTime = this.selectedSlot.endTime;
            bookingSuccessData.timeSlotId = this.selectedSlot.id;
            bookingSuccessData.time_slot_id = this.selectedSlot.id;
            bookingSuccessData.slotId = this.selectedSlot.id;
            bookingSuccessData.timeSlotIds = [this.selectedSlot.id];
            bookingSuccessData.time_slot_ids = [this.selectedSlot.id];
            bookingSuccessData.slotIds = [this.selectedSlot.id];
          }
          if (result && result.data) {
            bookingSuccessData.bookingId = result.data.id || result.data.bookingId;
            bookingSuccessData.booking_id = result.data.id || result.data.bookingId;
          }
          console.log("🔧 构建的预约成功数据:", bookingSuccessData);
          await this.venueStore.onBookingSuccess(bookingSuccessData);
          console.log("✅ 增强状态同步完成");
          this.selectedSlots = [];
          this.selectedSlot = null;
          console.log("🧹 已清除选中状态");
          this.$forceUpdate();
          console.log("🔄 强制更新页面");
        } catch (error) {
          console.error("❌ 时间段同步修复失败:", error);
          this.selectedSlots = [];
          this.selectedSlot = null;
          this.$forceUpdate();
        }
        common_vendor.index.hideLoading();
        try {
          common_vendor.index.$emit("bookingCreated", {
            type: "booking-created",
            bookingId: (_n = result == null ? void 0 : result.data) == null ? void 0 : _n.id,
            timestamp: Date.now()
          });
          console.log("📢 已发送预约创建通知");
        } catch (error) {
          console.warn("⚠️ 发送预约创建通知失败:", error);
        }
        this.showUserFeedback("success", "预约创建成功！正在跳转到支付页面...", {
          duration: 2e3,
          vibrate: true
        });
        console.log("🎉 预约创建成功！准备跳转到支付页面");
        console.log("📋 预约创建结果详情:", result);
        console.log("📋 result?.id:", result == null ? void 0 : result.id);
        console.log("📋 result?.orderId:", result == null ? void 0 : result.orderId);
        console.log("📋 result?.data:", result == null ? void 0 : result.data);
        console.log("📋 result?.data?.id:", (_o = result == null ? void 0 : result.data) == null ? void 0 : _o.id);
        let orderId = null;
        if (!result) {
          console.error("❌ 预约创建结果为空");
        } else {
          if (result.id) {
            orderId = result.id;
          } else if (result.orderId) {
            orderId = result.orderId;
          } else if (result.data && result.data.id) {
            orderId = result.data.id;
          } else if (result.data && result.data.orderId) {
            orderId = result.data.orderId;
          } else if (typeof result === "number") {
            orderId = result;
          }
        }
        console.log("🆔 提取的订单ID:", orderId);
        console.log("🆔 订单ID类型:", typeof orderId);
        if (orderId && (typeof orderId === "number" || typeof orderId === "string")) {
          console.log("✅ 订单ID有效，准备跳转到支付页面");
          common_vendor.index.redirectTo({
            url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`,
            success: () => {
              console.log("✅ 成功跳转到支付页面");
            },
            fail: (error) => {
              console.error("❌ 跳转支付页面失败:", error);
              common_vendor.index.navigateTo({
                url: `/pages/payment/index?orderId=${orderId}&type=booking&from=create`
              });
            }
          });
        } else {
          console.error("❌ 无法获取有效的订单ID，跳转到预约列表");
          console.error("❌ 原始结果:", result);
          this.showUserFeedback("confirm", '预约创建成功，但无法获取订单信息。请到"我的预约"中查看。', {
            confirmText: "查看预约",
            cancelText: "稍后查看",
            onConfirm: () => {
              common_vendor.index.redirectTo({
                url: "/pages/booking/list"
              });
            }
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("创建预约失败:", error);
        const errorMessage = error.message || "创建预约失败，请稍后重试";
        this.showUserFeedback("error", errorMessage, {
          duration: 3e3,
          vibrate: true,
          showModal: errorMessage.length > 20
        });
        this.recordPerformanceMetric("booking-creation-error", {
          error: error.message || "unknown",
          timestamp: Date.now()
        });
      } finally {
        this.isConfirmingBooking = false;
        console.log("[BookingCreate] 🔓 重置预约确认锁");
      }
    },
    // 验证表单
    validateForm() {
      try {
        this.recordPerformanceMetric("form-validation-start", {
          bookingType: this.bookingForm.bookingType,
          hasSelectedSlots: !!(this.selectedSlots && this.selectedSlots.length > 0),
          hasSelectedSlot: !!this.selectedSlot
        });
        console.log("[BookingCreate] 🔍 开始表单验证");
        if (!this.bookingForm.bookingType) {
          this.recordPerformanceMetric("form-validation-error", {
            field: "bookingType",
            error: "missing"
          });
          this.showUserFeedback("error", "请选择预约类型");
          return false;
        }
        const hasTimeSlots = this.selectedSlots && this.selectedSlots.length > 0 || this.selectedSlot;
        if (!hasTimeSlots) {
          this.recordPerformanceMetric("form-validation-error", {
            field: "timeSlots",
            error: "missing"
          });
          this.showUserFeedback("error", "请选择预约时间段");
          return false;
        }
        if (!this.venueId || !this.venue) {
          this.recordPerformanceMetric("form-validation-error", {
            field: "venue",
            error: "missing"
          });
          this.showUserFeedback("error", "场馆信息缺失，请重新选择");
          return false;
        }
        if (!this.selectedDate) {
          this.recordPerformanceMetric("form-validation-error", {
            field: "date",
            error: "missing"
          });
          this.showUserFeedback("error", "请选择预约日期");
          return false;
        }
        if (this.bookingForm.bookingType === "SHARED") {
          console.log("[BookingCreate] 🔍 验证拼场预约信息");
          if (!this.bookingForm.teamName || !this.bookingForm.teamName.trim()) {
            this.recordPerformanceMetric("form-validation-error", {
              field: "teamName",
              error: "empty",
              bookingType: "SHARED"
            });
            this.showUserFeedback("error", "请输入队伍名称");
            return false;
          }
          if (!this.bookingForm.contactInfo || !this.bookingForm.contactInfo.trim()) {
            this.recordPerformanceMetric("form-validation-error", {
              field: "contactInfo",
              error: "empty",
              bookingType: "SHARED"
            });
            this.showUserFeedback("error", "请输入联系方式");
            return false;
          }
          const contactInfo = this.bookingForm.contactInfo.trim();
          const phoneRegex = /^1[3-9]\d{9}$/;
          const wechatRegex = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/;
          if (!phoneRegex.test(contactInfo) && !wechatRegex.test(contactInfo)) {
            this.recordPerformanceMetric("form-validation-error", {
              field: "contactInfo",
              error: "invalid_format",
              bookingType: "SHARED"
            });
            this.showUserFeedback("error", "请输入有效的手机号或微信号");
            return false;
          }
          if (this.bookingForm.teamName.trim().length > 20) {
            this.recordPerformanceMetric("form-validation-error", {
              field: "teamName",
              error: "too_long",
              bookingType: "SHARED"
            });
            this.showUserFeedback("error", "队伍名称不能超过20个字符");
            return false;
          }
        }
        if (this.bookingForm.description && this.bookingForm.description.length > 200) {
          this.recordPerformanceMetric("form-validation-error", {
            field: "description",
            error: "too_long"
          });
          this.showUserFeedback("error", "预约描述不能超过200个字符");
          return false;
        }
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const unavailableSlots = this.selectedSlots.filter((slot) => slot.status !== "AVAILABLE");
          if (unavailableSlots.length > 0) {
            this.recordPerformanceMetric("form-validation-error", {
              field: "slotStatus",
              error: "unavailable",
              unavailableCount: unavailableSlots.length
            });
            const errorMessage = unavailableSlots.length === 1 ? `时间段 ${unavailableSlots[0].startTime}-${unavailableSlots[0].endTime} 已不可用，请重新选择` : `有 ${unavailableSlots.length} 个时间段已不可用，请重新选择`;
            this.showUserFeedback("error", errorMessage, {
              showModal: true,
              vibrate: true,
              confirmText: "重新选择",
              onConfirm: () => {
                this.loadTimeSlots();
                this.$nextTick(() => {
                  common_vendor.index.pageScrollTo({
                    selector: ".time-slots-container",
                    duration: 300
                  });
                });
              }
            });
            return false;
          }
        } else if (this.selectedSlot && this.selectedSlot.status !== "AVAILABLE") {
          this.recordPerformanceMetric("form-validation-error", {
            field: "slotStatus",
            error: "unavailable",
            slotId: this.selectedSlot.id
          });
          const errorMessage = `时间段 ${this.selectedSlot.startTime}-${this.selectedSlot.endTime} 已不可用，请重新选择`;
          this.showUserFeedback("error", errorMessage, {
            showModal: true,
            vibrate: true,
            confirmText: "重新选择",
            onConfirm: () => {
              this.loadTimeSlots();
              this.$nextTick(() => {
                common_vendor.index.pageScrollTo({
                  selector: ".time-slots-container",
                  duration: 300
                });
              });
            }
          });
          return false;
        }
        console.log("[BookingCreate] ✅ 表单验证通过");
        this.recordPerformanceMetric("form-validation-success", {
          bookingType: this.bookingForm.bookingType,
          slotsCount: this.selectedSlots ? this.selectedSlots.length : this.selectedSlot ? 1 : 0,
          hasDescription: !!this.bookingForm.description
        });
        return true;
      } catch (error) {
        console.error("[BookingCreate] ❌ 表单验证异常:", error);
        this.recordPerformanceMetric("form-validation-exception", {
          error: error.message,
          stack: error.stack
        });
        this.showUserFeedback("error", "表单验证失败，请检查输入信息");
        return false;
      }
    },
    // 🗜️ 数据压缩方法
    compressBookingData() {
      try {
        const baseData = {
          venueId: parseInt(this.venueId),
          date: this.selectedDate,
          bookingType: this.bookingForm.bookingType,
          description: this.bookingForm.description || ""
        };
        if (this.selectedSlots && this.selectedSlots.length > 0) {
          const sortedSlots = [...this.selectedSlots].sort((a, b) => {
            const timeA = a.startTime.split(":").map(Number);
            const timeB = b.startTime.split(":").map(Number);
            if (timeA[0] !== timeB[0])
              return timeA[0] - timeB[0];
            return timeA[1] - timeB[1];
          });
          baseData.startTime = sortedSlots[0].startTime;
          baseData.endTime = sortedSlots[sortedSlots.length - 1].endTime;
          baseData.slotIds = sortedSlots.map((slot) => slot.id);
        } else if (this.selectedSlot) {
          baseData.startTime = this.selectedSlot.startTime;
          baseData.endTime = this.selectedSlot.endTime;
          baseData.slotId = this.selectedSlot.id;
        }
        if (this.bookingForm.bookingType === "SHARED") {
          baseData.teamName = this.bookingForm.teamName;
          baseData.contactInfo = this.bookingForm.contactInfo;
          baseData.maxParticipants = 2;
        }
        const compressedData = {};
        Object.keys(baseData).forEach((key) => {
          const value = baseData[key];
          if (value !== null && value !== void 0 && value !== "") {
            compressedData[key] = value;
          }
        });
        console.log("[BookingCreate] 🗜️ 数据压缩完成:", {
          原始大小: JSON.stringify(baseData).length,
          压缩后大小: JSON.stringify(compressedData).length,
          压缩率: ((1 - JSON.stringify(compressedData).length / JSON.stringify(baseData).length) * 100).toFixed(2) + "%"
        });
        return compressedData;
      } catch (error) {
        console.error("[BookingCreate] ❌ 数据压缩失败:", error);
        return {};
      }
    },
    // 📊 性能监控方法
    recordPerformanceMetric(event, data = {}) {
      try {
        const timestamp = Date.now();
        const metric = {
          event,
          timestamp,
          page: "booking-create",
          ...data
        };
        console.log("[BookingCreate] 📊 性能监控:", metric);
        switch (event) {
          case "page-load-start":
            this.performanceMetrics.loadStartTime = timestamp;
            break;
          case "page-load-complete":
            if (this.performanceMetrics.loadStartTime) {
              const loadTime = timestamp - this.performanceMetrics.loadStartTime;
              console.log("[BookingCreate] 📊 页面加载耗时:", loadTime + "ms");
            }
            break;
          case "booking-confirm-start":
            this.performanceMetrics.confirmStartTime = timestamp;
            break;
          case "booking-confirm-complete":
            if (this.performanceMetrics.confirmStartTime) {
              const confirmTime = timestamp - this.performanceMetrics.confirmStartTime;
              console.log("[BookingCreate] 📊 预约确认耗时:", confirmTime + "ms");
            }
            break;
          case "network-request-start":
            this.performanceMetrics.networkStartTime = timestamp;
            break;
          case "network-request-complete":
            if (this.performanceMetrics.networkStartTime) {
              const networkTime = timestamp - this.performanceMetrics.networkStartTime;
              console.log("[BookingCreate] 📊 网络请求耗时:", networkTime + "ms");
            }
            break;
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 性能监控记录失败:", error);
      }
    },
    // 🎯 用户体验优化方法
    showUserFeedback(type, message, options = {}) {
      try {
        if (!message) {
          message = "操作完成";
        }
        if (typeof message !== "string") {
          message = String(message);
        }
        const { duration = 2e3, icon = "none", showCancel = false, confirmText = "确定" } = options;
        switch (type) {
          case "success":
            common_vendor.index.showToast({
              title: message,
              icon: "success",
              duration: Math.min(duration, 3e3)
              // 成功提示不超过3秒
            });
            common_vendor.index.vibrateShort();
            break;
          case "error":
            if (message && message.length > 20 || options.useModal) {
              common_vendor.index.showModal({
                title: "提示",
                content: message,
                showCancel,
                confirmText,
                confirmColor: "#ff4757",
                success: (res) => {
                  if (res.confirm && options.onConfirm) {
                    options.onConfirm();
                  } else if (res.cancel && options.onCancel) {
                    options.onCancel();
                  }
                }
              });
            } else {
              common_vendor.index.showToast({
                title: message,
                icon: "error",
                duration: Math.max(duration, 2500)
                // 错误提示至少显示2.5秒
              });
            }
            common_vendor.index.vibrateLong();
            break;
          case "warning":
            common_vendor.index.showToast({
              title: message,
              icon: "none",
              duration,
              image: "/static/icons/warning.png"
              // 如果有警告图标
            });
            break;
          case "loading":
            common_vendor.index.showLoading({
              title: message,
              mask: true
              // 添加遮罩防止用户操作
            });
            this.uxState.showLoading = true;
            this.uxState.loadingText = message;
            break;
          case "hide-loading":
            common_vendor.index.hideLoading();
            this.uxState.showLoading = false;
            break;
          case "confirm":
            return new Promise((resolve) => {
              common_vendor.index.showModal({
                title: options.title || "确认",
                content: message,
                showCancel: true,
                confirmText,
                cancelText: options.cancelText || "取消",
                confirmColor: options.confirmColor || "#007aff",
                success: (res) => {
                  resolve(res.confirm);
                },
                fail: () => {
                  resolve(false);
                }
              });
            });
          default:
            common_vendor.index.showToast({
              title: message,
              icon,
              duration
            });
        }
        console.log("[BookingCreate] 🎯 用户反馈:", { type, message, options });
      } catch (error) {
        console.error("[BookingCreate] ❌ 用户反馈显示失败:", error);
        try {
          common_vendor.index.showToast({
            title: message || "操作失败",
            icon: "none",
            duration: 2e3
          });
        } catch (fallbackError) {
          console.error("[BookingCreate] ❌ 降级用户反馈也失败:", fallbackError);
        }
      }
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 🧹 清除不可用的选中状态
    clearUnavailableSelections(unavailableSlots) {
      var _a, _b;
      try {
        if (this.isMultiSlot) {
          const originalCount = this.selectedSlots.length;
          this.selectedSlots = this.selectedSlots.filter(
            (slot) => !unavailableSlots.some(
              (unavailable) => unavailable.id === slot.id || unavailable.startTime === slot.startTime && unavailable.endTime === slot.endTime
            )
          );
          const removedCount = originalCount - this.selectedSlots.length;
          console.log(`[BookingCreate] 🧹 多选模式：移除了${removedCount}个不可用时间段，剩余${this.selectedSlots.length}个`);
        } else {
          const isCurrentSlotUnavailable = unavailableSlots.some(
            (unavailable) => {
              var _a2, _b2, _c;
              return unavailable.id === ((_a2 = this.selectedSlot) == null ? void 0 : _a2.id) || unavailable.startTime === ((_b2 = this.selectedSlot) == null ? void 0 : _b2.startTime) && unavailable.endTime === ((_c = this.selectedSlot) == null ? void 0 : _c.endTime);
            }
          );
          if (isCurrentSlotUnavailable) {
            console.log(`[BookingCreate] 🧹 单选模式：清除不可用的选中时间段 ${(_a = this.selectedSlot) == null ? void 0 : _a.startTime}-${(_b = this.selectedSlot) == null ? void 0 : _b.endTime}`);
            this.selectedSlot = null;
          }
        }
      } catch (error) {
        console.warn("[BookingCreate] ⚠️ 清除不可用选中状态时出错:", error);
      }
    },
    // 📝 生成验证错误消息
    generateValidationErrorMessage(unavailableSlots) {
      try {
        if (unavailableSlots.length === 1) {
          const slot = unavailableSlots[0];
          return `您选择的时间段${slot.startTime}-${slot.endTime}${slot.reason}，请重新选择其他可用时间段。`;
        } else {
          const slotDetails = unavailableSlots.map(
            (slot) => `${slot.startTime}-${slot.endTime}(${slot.reason})`
          ).join("\n");
          return `以下${unavailableSlots.length}个时间段已不可用：
${slotDetails}

请重新选择其他可用时间段。`;
        }
      } catch (error) {
        console.warn("[BookingCreate] ⚠️ 生成错误消息时出错:", error);
        return "选中的时间段已不可用，请重新选择其他可用时间段。";
      }
    },
    // 🔄 实时验证时间段状态
    async validateTimeSlotsRealtime() {
      var _a;
      try {
        const validationStartTime = /* @__PURE__ */ new Date();
        console.log("[BookingCreate] 🔄 开始实时验证时间段状态");
        console.log("[BookingCreate] ⏰ 时间调试信息:", {
          validationStartTime: validationStartTime.toISOString(),
          localTime: validationStartTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }),
          timestamp: validationStartTime.getTime(),
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          timezoneOffset: validationStartTime.getTimezoneOffset(),
          selectedDate: this.selectedDate,
          venueId: this.venueId,
          currentHour: validationStartTime.getHours(),
          currentMinute: validationStartTime.getMinutes(),
          currentSecond: validationStartTime.getSeconds()
        });
        this.recordPerformanceMetric("realtime-validation-start");
        this.showUserFeedback("loading", "正在验证时间段状态...");
        let selectedSlots = [];
        if (this.selectedSlots && Array.isArray(this.selectedSlots) && this.selectedSlots.length > 0) {
          selectedSlots = this.selectedSlots;
        } else if (this.selectedSlot) {
          selectedSlots = [this.selectedSlot];
        }
        console.log("[BookingCreate] 🔍 原始选中数据:", {
          selectedSlotsArray: this.selectedSlots,
          selectedSlotsLength: (_a = this.selectedSlots) == null ? void 0 : _a.length,
          selectedSlot: this.selectedSlot,
          finalSelectedSlots: selectedSlots
        });
        const validSelectedSlots = selectedSlots.filter((slot) => {
          return slot && typeof slot === "object" && slot.id && slot.startTime && slot.endTime;
        });
        console.log("[BookingCreate] 🔍 过滤后的有效时间段:", validSelectedSlots);
        if (!validSelectedSlots || validSelectedSlots.length === 0) {
          this.showUserFeedback("hide-loading");
          console.warn("[BookingCreate] ⚠️ 没有有效的选中时间段");
          this.showUserFeedback("error", "请先选择时间段");
          return false;
        }
        console.log("[BookingCreate] 📋 准备验证的时间段:", validSelectedSlots.map((slot) => `${slot.startTime}-${slot.endTime} (ID: ${slot.id})`).join(", "));
        const response = await api_timeslot.refreshTimeSlotStatus(this.venueId, this.selectedDate);
        this.recordPerformanceMetric("realtime-validation-network-complete");
        console.log("[BookingCreate] 🔍 refreshTimeSlotStatus响应详情:", {
          hasResponse: !!response,
          responseType: typeof response,
          hasSuccess: response && "success" in response,
          successValue: response == null ? void 0 : response.success,
          hasData: response && "data" in response,
          dataType: typeof (response == null ? void 0 : response.data),
          isDataArray: Array.isArray(response == null ? void 0 : response.data),
          dataLength: Array.isArray(response == null ? void 0 : response.data) ? response.data.length : "N/A",
          message: response == null ? void 0 : response.message
        });
        if (response && response.success && Array.isArray(response.data)) {
          const latestSlots = response.data;
          console.log("[BookingCreate] 📊 获取到最新时间段状态:", latestSlots.length, "个时间段");
          console.log("[BookingCreate] 🔍 最新时间段详情:", latestSlots.map((slot) => ({
            id: slot.id,
            timeRange: `${slot.startTime}-${slot.endTime}`,
            status: slot.status
          })));
          const unavailableSlots = [];
          console.log("[BookingCreate] 🔍 开始验证选中时间段:", {
            selectedCount: validSelectedSlots.length,
            latestCount: latestSlots.length,
            selectedSlots: validSelectedSlots.map((s) => ({
              id: s.id,
              timeRange: `${s.startTime}-${s.endTime}`,
              status: s.status
            })),
            latestSlots: latestSlots.map((s) => ({
              id: s.id,
              timeRange: `${s.startTime}-${s.endTime}`,
              status: s.status
            }))
          });
          console.log("[BookingCreate] 📊 API返回数据分析:", {
            totalSlots: latestSlots.length,
            slotIds: latestSlots.map((s) => s.id),
            timeRanges: latestSlots.map((s) => `${s.startTime}-${s.endTime}`),
            statuses: latestSlots.map((s) => s.status),
            uniqueIds: [...new Set(latestSlots.map((s) => s.id))].length,
            duplicateIds: latestSlots.length - [...new Set(latestSlots.map((s) => s.id))].length
          });
          for (const selectedSlot of validSelectedSlots) {
            console.log("[BookingCreate] 🔍 正在验证时间段:", {
              id: selectedSlot.id,
              timeRange: `${selectedSlot.startTime}-${selectedSlot.endTime}`,
              status: selectedSlot.status
            });
            let latestSlot = null;
            let matchMethod = "none";
            if (selectedSlot.id) {
              latestSlot = latestSlots.find((slot) => {
                const match = slot.id && slot.id.toString() === selectedSlot.id.toString();
                if (match) {
                  console.log("[BookingCreate] ✅ ID精确匹配成功:", {
                    selectedId: selectedSlot.id,
                    foundId: slot.id,
                    timeRange: `${slot.startTime}-${slot.endTime}`
                  });
                }
                return match;
              });
              if (latestSlot)
                matchMethod = "id-exact";
            }
            if (false)
              ;
            if (false)
              ;
            if (!latestSlot) {
              console.warn("[BookingCreate] ⚠️ 未找到匹配的时间段，跳过验证:", {
                selectedSlotId: selectedSlot.id,
                selectedTime: `${selectedSlot.startTime}-${selectedSlot.endTime}`,
                selectedDate: selectedSlot.date,
                currentSelectedDate: this.selectedDate,
                reason: "可能是不同日期的时间段"
              });
              continue;
            }
            console.log("[BookingCreate] 🔍 匹配过程详情:", {
              selectedSlot: {
                id: selectedSlot.id,
                startTime: selectedSlot.startTime,
                endTime: selectedSlot.endTime,
                status: selectedSlot.status
              },
              matchMethod,
              found: !!latestSlot,
              matchedSlot: latestSlot ? {
                id: latestSlot.id,
                startTime: latestSlot.startTime,
                endTime: latestSlot.endTime,
                status: latestSlot.status
              } : null,
              availableSlots: latestSlots.length,
              searchAttempts: {
                idSearch: selectedSlot.id ? "attempted" : "skipped",
                timeExactSearch: selectedSlot.startTime && selectedSlot.endTime ? "attempted" : "skipped",
                timeFuzzySearch: !latestSlot && selectedSlot.startTime && selectedSlot.endTime ? "attempted" : "skipped"
              }
            });
            console.log("[BookingCreate] 🔍 匹配结果:", {
              found: !!latestSlot,
              selectedSlot: {
                id: selectedSlot.id,
                timeRange: `${selectedSlot.startTime}-${selectedSlot.endTime}`,
                status: selectedSlot.status
              },
              matchedSlot: latestSlot ? {
                id: latestSlot.id,
                timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                status: latestSlot.status
              } : null
            });
            if (!latestSlot) {
              const analysisResult = {
                selectedSlot,
                searchId: selectedSlot.id,
                searchTime: `${selectedSlot.startTime}-${selectedSlot.endTime}`,
                totalAvailableSlots: latestSlots.length,
                availableIds: latestSlots.map((s) => s.id),
                availableTimes: latestSlots.map((s) => `${s.startTime}-${s.endTime}`),
                possibleMatches: [],
                analysisDetails: {
                  hasId: !!selectedSlot.id,
                  hasTime: !!(selectedSlot.startTime && selectedSlot.endTime),
                  idExistsInApi: selectedSlot.id ? latestSlots.some((s) => s.id && s.id.toString() === selectedSlot.id.toString()) : false,
                  timeExistsInApi: selectedSlot.startTime && selectedSlot.endTime ? latestSlots.some((s) => s.startTime === selectedSlot.startTime && s.endTime === selectedSlot.endTime) : false
                }
              };
              if (selectedSlot.startTime && selectedSlot.endTime) {
                const similarSlots = latestSlots.filter((slot) => {
                  const startMatch = slot.startTime && slot.startTime.includes(selectedSlot.startTime.split(":")[0]);
                  const endMatch = slot.endTime && slot.endTime.includes(selectedSlot.endTime.split(":")[0]);
                  return startMatch || endMatch;
                });
                analysisResult.possibleMatches = similarSlots.map((s) => ({
                  id: s.id,
                  time: `${s.startTime}-${s.endTime}`,
                  status: s.status
                }));
              }
              console.warn("[BookingCreate] ⚠️ 时间段匹配失败 - 详细分析:", analysisResult);
              let suggestedSlot = null;
              if (selectedSlot.startTime && latestSlots.length > 0) {
                const targetHour = parseInt(selectedSlot.startTime.split(":")[0]);
                suggestedSlot = latestSlots.filter((s) => s.status === "AVAILABLE").sort((a, b) => {
                  const aHour = parseInt(a.startTime.split(":")[0]);
                  const bHour = parseInt(b.startTime.split(":")[0]);
                  return Math.abs(aHour - targetHour) - Math.abs(bHour - targetHour);
                })[0];
                if (suggestedSlot) {
                  console.log("[BookingCreate] 💡 建议替代时间段:", {
                    original: `${selectedSlot.startTime}-${selectedSlot.endTime}`,
                    suggested: `${suggestedSlot.startTime}-${suggestedSlot.endTime}`,
                    suggestedId: suggestedSlot.id
                  });
                }
              }
              unavailableSlots.push({
                ...selectedSlot,
                reason: "时间段不存在",
                newStatus: "NOT_FOUND",
                analysis: analysisResult,
                suggestedAlternative: suggestedSlot
              });
            } else if (latestSlot.status !== "AVAILABLE") {
              const statusChangeTime = /* @__PURE__ */ new Date();
              const slotTimeInfo = {
                date: this.selectedDate,
                startTime: latestSlot.startTime,
                endTime: latestSlot.endTime,
                timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`
              };
              let slotStartDateTime = null;
              let slotEndDateTime = null;
              let shouldBeExpired = false;
              const currentDateStr = statusChangeTime.toISOString().split("T")[0];
              const isToday = this.selectedDate === currentDateStr;
              const isFutureDate = this.selectedDate > currentDateStr;
              try {
                const [startHour, startMinute] = latestSlot.startTime.split(":").map(Number);
                const [endHour, endMinute] = latestSlot.endTime.split(":").map(Number);
                const [year, month, day] = this.selectedDate.split("-").map(Number);
                slotStartDateTime = /* @__PURE__ */ new Date();
                slotEndDateTime = /* @__PURE__ */ new Date();
                slotStartDateTime.setFullYear(year, month - 1, day);
                slotStartDateTime.setHours(startHour, startMinute, 0, 0);
                slotEndDateTime.setFullYear(year, month - 1, day);
                slotEndDateTime.setHours(endHour, endMinute, 0, 0);
                shouldBeExpired = statusChangeTime > slotEndDateTime;
                if (isFutureDate) {
                  shouldBeExpired = false;
                }
                console.log("[BookingCreate] ⏰ 时间段状态验证 (详细):", {
                  selectedDate: this.selectedDate,
                  currentDateStr,
                  timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                  currentStatus: latestSlot.status,
                  slotStartDateTime: slotStartDateTime.toISOString(),
                  slotEndDateTime: slotEndDateTime.toISOString(),
                  currentTime: statusChangeTime.toISOString(),
                  shouldBeExpired,
                  isToday,
                  isFutureDate,
                  isPastDate: this.selectedDate < currentDateStr,
                  timeDiffFromEndMinutes: Math.round((slotEndDateTime - statusChangeTime) / (1e3 * 60)),
                  dateComparison: {
                    selectedDate: this.selectedDate,
                    currentDate: currentDateStr,
                    comparison: this.selectedDate === currentDateStr ? "same" : this.selectedDate > currentDateStr ? "future" : "past"
                  }
                });
              } catch (timeError) {
                console.error("[BookingCreate] ❌ 时间计算错误:", timeError);
              }
              if (isFutureDate && latestSlot.status === "EXPIRED") {
                console.error("[BookingCreate] 🚨 强制修正未来日期的错误EXPIRED状态:", {
                  slotId: latestSlot.id,
                  timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                  selectedDate: this.selectedDate,
                  currentDate: currentDateStr
                });
                continue;
              }
              if (isToday && latestSlot.status === "EXPIRED" && !shouldBeExpired) {
                console.warn("[BookingCreate] ⚠️ 发现今日错误的EXPIRED状态，时间段实际未过期:", {
                  slotId: latestSlot.id,
                  timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                  currentTime: statusChangeTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }),
                  slotEndTime: slotEndDateTime ? slotEndDateTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }) : "unknown",
                  shouldBeExpired
                });
                continue;
              }
              if (latestSlot.status !== "AVAILABLE") {
                console.warn("[BookingCreate] ⚠️ 时间段状态已变更:", {
                  slotId: latestSlot.id,
                  timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                  oldStatus: selectedSlot.status,
                  newStatus: latestSlot.status,
                  isValidExpired: latestSlot.status === "EXPIRED" ? shouldBeExpired : "N/A"
                });
                unavailableSlots.push({
                  ...latestSlot,
                  reason: latestSlot.status === "BOOKED" ? "已被预约" : latestSlot.status === "MAINTENANCE" ? "维护中" : latestSlot.status === "EXPIRED" ? "已过期" : "不可用",
                  newStatus: latestSlot.status
                });
              }
            } else {
              console.log("[BookingCreate] ✅ 时间段状态正常:", {
                slotId: latestSlot.id,
                timeRange: `${latestSlot.startTime}-${latestSlot.endTime}`,
                status: latestSlot.status
              });
            }
          }
          this.showUserFeedback("hide-loading");
          if (unavailableSlots.length > 0) {
            console.error("[BookingCreate] ❌ 发现不可用时间段:", unavailableSlots.map((slot) => ({
              id: slot.id,
              timeRange: `${slot.startTime}-${slot.endTime}`,
              reason: slot.reason,
              status: slot.newStatus
            })));
            console.error("[BookingCreate] 🚨 关键问题分析:", {
              selectedDate: this.selectedDate,
              currentDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
              isToday: this.selectedDate === (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
              isTomorrow: this.selectedDate === new Date(Date.now() + 24 * 60 * 60 * 1e3).toISOString().split("T")[0],
              unavailableSlots: unavailableSlots.map((s) => s.timeRange)
            });
            this.recordPerformanceMetric("realtime-validation-failed", {
              unavailableCount: unavailableSlots.length,
              reasons: unavailableSlots.map((slot) => slot.reason),
              statuses: unavailableSlots.map((slot) => slot.newStatus)
            });
            try {
              this.venueStore.updateTimeSlotsStatus(this.venueId, this.selectedDate, latestSlots);
              console.log("[BookingCreate] 🔄 本地时间段状态已更新");
            } catch (updateError) {
              console.warn("[BookingCreate] ⚠️ 更新本地状态失败:", updateError);
            }
            this.clearUnavailableSelections(unavailableSlots);
            const errorDetails = this.generateValidationErrorMessage(unavailableSlots);
            this.showUserFeedback("error", errorDetails, {
              useModal: true,
              confirmText: "重新选择",
              onConfirm: () => {
                this.$emit("refresh-timeslots");
                this.$nextTick(() => {
                  common_vendor.index.pageScrollTo({
                    selector: ".time-slots-container",
                    duration: 300
                  });
                });
              }
            });
            this.$emit("refresh-timeslots");
            return false;
          }
          console.log("[BookingCreate] ✅ 所有选中时间段状态验证通过");
          this.recordPerformanceMetric("realtime-validation-success");
          return true;
        } else {
          console.error("[BookingCreate] ❌ 获取时间段状态失败 - API响应:", response);
          const errorMessage = response && response.message || "获取时间段状态失败";
          this.showUserFeedback("hide-loading");
          this.recordPerformanceMetric("realtime-validation-api-error", {
            error: errorMessage,
            responseSuccess: response == null ? void 0 : response.success,
            hasData: !!(response == null ? void 0 : response.data)
          });
          throw new Error(errorMessage);
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 实时验证时间段状态失败:", error);
        this.showUserFeedback("hide-loading");
        this.recordPerformanceMetric("realtime-validation-error", {
          error: error.message || error.toString() || "unknown",
          stack: error.stack
        });
        const errorMessage = error.message || error.toString() || "未知错误";
        if (errorMessage.includes("网络") || errorMessage.includes("timeout") || errorMessage.includes("Network")) {
          this.showUserFeedback("error", "网络连接异常，请检查网络后重试", { duration: 3e3 });
        } else {
          this.showUserFeedback("error", "验证时间段状态失败，请重试", { duration: 3e3 });
        }
        return false;
      }
    },
    // 🎧 设置事件监听器
    setupEventListeners() {
      console.log("[BookingCreate] 🎧 设置事件监听器");
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.$on) {
        common_vendor.index.$on("timeslots-status-updated", this.handleTimeSlotsStatusUpdated);
        common_vendor.index.$on("timeslots-refreshed", this.handleTimeSlotsRefreshed);
        common_vendor.index.$on("booking-status-changed", this.handleBookingStatusChanged);
        common_vendor.index.$on("orderCancelled", this.handleOrderCancelled);
        common_vendor.index.$on("timeslot-updated", this.handleTimeslotUpdated);
        common_vendor.index.$on("force-refresh-timeslots", this.handleForceRefreshTimeslots);
        console.log("[BookingCreate] ✅ 事件监听器设置完成");
      }
    },
    // 🎧 移除事件监听器
    removeEventListeners() {
      console.log("[BookingCreate] 🎧 移除事件监听器");
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.$off) {
        common_vendor.index.$off("timeslots-status-updated", this.handleTimeSlotsStatusUpdated);
        common_vendor.index.$off("timeslots-refreshed", this.handleTimeSlotsRefreshed);
        common_vendor.index.$off("booking-status-changed", this.handleBookingStatusChanged);
        common_vendor.index.$off("orderCancelled", this.handleOrderCancelled);
        common_vendor.index.$off("timeslot-updated", this.handleTimeslotUpdated);
        common_vendor.index.$off("force-refresh-timeslots", this.handleForceRefreshTimeslots);
        console.log("[BookingCreate] ✅ 事件监听器移除完成");
      }
    },
    // 🔄 处理时间段状态更新事件
    handleTimeSlotsStatusUpdated(eventData) {
      var _a;
      console.log("[BookingCreate] 🔄 收到时间段状态更新事件:", eventData);
      try {
        if (eventData.venueId === this.venueId && eventData.date === this.selectedDate) {
          console.log("[BookingCreate] 🎯 处理当前页面的时间段状态更新");
          this.validateSelectedSlotsAfterUpdate(eventData.timeSlots);
          this.recordPerformanceMetric("timeslots-auto-updated", {
            source: eventData.source || "unknown",
            slotsCount: ((_a = eventData.timeSlots) == null ? void 0 : _a.length) || 0
          });
          this.showUserFeedback("success", "时间段状态已更新", { duration: 2e3 });
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理时间段状态更新失败:", error);
      }
    },
    // 🔄 处理时间段刷新事件
    handleTimeSlotsRefreshed(eventData) {
      console.log("[BookingCreate] 🔄 收到时间段刷新事件:", eventData);
      try {
        if (eventData.venueId === this.venueId && eventData.date === this.selectedDate) {
          console.log("[BookingCreate] 🎯 处理当前页面的时间段刷新");
          this.validateSelectedSlotsAfterUpdate(eventData.timeSlots);
          this.recordPerformanceMetric("timeslots-refreshed", {
            trigger: eventData.trigger || "unknown"
          });
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理时间段刷新失败:", error);
      }
    },
    // 🔄 处理预约状态变更事件
    handleBookingStatusChanged(eventData) {
      console.log("[BookingCreate] 🔄 收到预约状态变更事件:", eventData);
      try {
        if (eventData.venueId === this.venueId && eventData.date === this.selectedDate) {
          console.log("[BookingCreate] 🎯 处理当前页面的预约状态变更");
          this.refreshTimeSlotStatusSafe();
          this.recordPerformanceMetric("booking-status-changed", {
            bookingId: eventData.bookingId,
            status: eventData.status
          });
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理预约状态变更失败:", error);
      }
    },
    // 🔄 处理订单取消事件
    handleOrderCancelled(eventData) {
      console.log("[BookingCreate] 🔄 收到订单取消事件:", eventData);
      try {
        console.log("[BookingCreate] 🎯 订单取消，立即刷新时间段状态");
        this.refreshTimeSlotStatusSafe();
        this.recordPerformanceMetric("order-cancelled", {
          orderId: eventData.orderId,
          type: eventData.type
        });
        this.showUserFeedback("info", "时间段状态已更新", {
          duration: 2e3
        });
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理订单取消事件失败:", error);
      }
    },
    // 🔄 处理时间段更新事件
    handleTimeslotUpdated(eventData) {
      console.log("[BookingCreate] 🚨🚨🚨 收到时间段更新事件 🚨🚨🚨");
      console.log("[BookingCreate] 事件数据:", eventData);
      console.log("[BookingCreate] 当前页面信息:", {
        venueId: this.venueId,
        selectedDate: this.selectedDate
      });
      try {
        if (eventData.venueId == this.venueId && eventData.date === this.selectedDate) {
          console.log("[BookingCreate] 🎯🎯🎯 确认是当前页面的时间段更新事件 🎯🎯🎯");
          if (eventData.immediate || eventData.action === "booking-cancelled-immediate") {
            console.log("[BookingCreate] 🚀 立即更新事件，使用统一时间段管理器");
            if (eventData.startTime && eventData.endTime) {
              this.useUnifiedTimeSlotManager(eventData);
            }
            this.refreshTimeSlotStatusSafe(true);
            setTimeout(() => {
              this.refreshTimeSlotStatusSafe(true);
            }, 500);
          } else if (eventData.action === "booking-cancelled" && eventData.bookingType === "SHARED") {
            console.log("[BookingCreate] 🎯 拼场订单取消，使用统一时间段管理器");
            if (eventData.startTime && eventData.endTime) {
              this.useUnifiedTimeSlotManager(eventData);
            }
            this.refreshTimeSlotStatusSafe(true);
            setTimeout(() => {
              this.refreshTimeSlotStatusSafe(true);
            }, 500);
            setTimeout(() => {
              this.refreshTimeSlotStatusSafe(true);
            }, 1500);
          } else {
            this.refreshTimeSlotStatusSafe();
          }
          this.recordPerformanceMetric("timeslot-updated", {
            action: eventData.action,
            slotIds: eventData.slotIds,
            bookingType: eventData.bookingType
          });
          if (eventData.action === "booking-cancelled") {
            const message = eventData.bookingType === "SHARED" ? "拼场订单已取消，时间段状态已更新" : "预约已取消，时间段状态已更新";
            this.showUserFeedback("info", message, {
              duration: 2e3
            });
          }
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理时间段更新事件失败:", error);
      }
    },
    // 🎯 使用统一时间段管理器立即释放时间段
    async useUnifiedTimeSlotManager(eventData) {
      try {
        console.log("[BookingCreate] 🎯 使用统一时间段管理器立即释放时间段:", eventData);
        const { default: unifiedTimeSlotManager } = await "../../utils/unified-timeslot-manager.js";
        if (unifiedTimeSlotManager && typeof unifiedTimeSlotManager.immediateReleaseTimeSlots === "function") {
          await unifiedTimeSlotManager.immediateReleaseTimeSlots(
            eventData.venueId,
            eventData.date,
            eventData.startTime,
            eventData.endTime,
            eventData.bookingType || "EXCLUSIVE"
          );
          console.log("[BookingCreate] 🎯 统一时间段管理器立即释放完成");
        } else {
          console.warn("[BookingCreate] 统一时间段管理器不可用或方法不存在");
        }
      } catch (error) {
        console.error("[BookingCreate] 使用统一时间段管理器失败:", error);
      }
    },
    // 🚨 处理强制刷新时间段事件
    handleForceRefreshTimeslots(eventData) {
      console.log("[BookingCreate] 🚨🚨🚨 收到强制刷新时间段事件 🚨🚨🚨");
      console.log("[BookingCreate] 强制刷新事件数据:", eventData);
      try {
        if (eventData.venueId == this.venueId && eventData.date === this.selectedDate) {
          console.log("[BookingCreate] 🎯 执行强制刷新时间段");
          this.refreshTimeSlotStatusSafe(true);
          setTimeout(() => {
            console.log("[BookingCreate] 🔄 第二次强制刷新");
            this.refreshTimeSlotStatusSafe(true);
          }, 200);
          setTimeout(() => {
            console.log("[BookingCreate] 🔄 第三次强制刷新");
            this.refreshTimeSlotStatusSafe(true);
          }, 800);
          this.recordPerformanceMetric("force-refresh-timeslots", {
            reason: eventData.reason,
            venueId: eventData.venueId,
            date: eventData.date
          });
          this.showUserFeedback("info", "时间段状态已强制更新", {
            duration: 2e3
          });
        } else {
          console.log("[BookingCreate] 🔍 强制刷新事件不匹配当前页面，忽略");
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 处理强制刷新时间段事件失败:", error);
      }
    },
    // ✅ 验证更新后的选中时间段
    validateSelectedSlotsAfterUpdate(latestSlots) {
      try {
        console.log("[BookingCreate] ✅ 验证更新后的选中时间段");
        if (!latestSlots || !Array.isArray(latestSlots)) {
          console.warn("[BookingCreate] ⚠️ 最新时间段数据无效");
          return;
        }
        const selectedSlots = this.isMultiSlot ? this.selectedSlots : [this.selectedSlot];
        if (!selectedSlots || selectedSlots.length === 0) {
          return;
        }
        const invalidSlots = [];
        for (const selectedSlot of selectedSlots) {
          if (!selectedSlot)
            continue;
          const latestSlot = latestSlots.find(
            (slot) => slot.id === selectedSlot.id || slot.startTime === selectedSlot.startTime && slot.endTime === selectedSlot.endTime
          );
          if (!latestSlot || latestSlot.status !== "AVAILABLE") {
            invalidSlots.push({
              ...selectedSlot,
              newStatus: (latestSlot == null ? void 0 : latestSlot.status) || "NOT_FOUND",
              reason: !latestSlot ? "时间段已删除" : latestSlot.status === "BOOKED" ? "已被其他用户预约" : latestSlot.status === "MAINTENANCE" ? "进入维护状态" : "状态异常"
            });
          }
        }
        if (invalidSlots.length > 0) {
          console.warn("[BookingCreate] ⚠️ 发现无效的选中时间段:", invalidSlots);
          if (this.isMultiSlot) {
            this.selectedSlots = this.selectedSlots.filter(
              (slot) => !invalidSlots.some((invalid) => invalid.id === slot.id)
            );
          } else {
            const isCurrentSlotInvalid = invalidSlots.some(
              (invalid) => {
                var _a;
                return invalid.id === ((_a = this.selectedSlot) == null ? void 0 : _a.id);
              }
            );
            if (isCurrentSlotInvalid) {
              this.selectedSlot = null;
            }
          }
          const message = invalidSlots.length === 1 ? `时间段${invalidSlots[0].startTime}-${invalidSlots[0].endTime}${invalidSlots[0].reason}，请重新选择` : `有${invalidSlots.length}个时间段状态已变更，请重新选择`;
          this.showUserFeedback("warning", message, { duration: 4e3 });
          this.recordPerformanceMetric("selected-slots-invalidated", {
            invalidCount: invalidSlots.length,
            reasons: invalidSlots.map((slot) => slot.reason)
          });
        } else {
          console.log("[BookingCreate] ✅ 所有选中时间段仍然有效");
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 验证选中时间段失败:", error);
      }
    },
    // 🎯 显示预约确认对话框
    async showConfirmationDialog() {
      try {
        if (this.showConfirmDialog) {
          console.warn("[BookingCreate] ⚠️ 确认弹窗已显示，忽略重复调用");
          return false;
        }
        const confirmInfo = this.buildConfirmationInfo();
        this.confirmDialogData = confirmInfo;
        this.showConfirmDialog = true;
        const result = await new Promise((resolve) => {
          this.confirmDialogResolve = resolve;
        });
        return result;
      } catch (error) {
        console.error("[BookingCreate] ❌ 显示确认对话框异常:", error);
        this.showConfirmDialog = false;
        this.confirmDialogData = null;
        this.confirmDialogResolve = null;
        return false;
      }
    },
    // 🏗️ 构建确认信息
    buildConfirmationInfo() {
      var _a;
      try {
        const venue = this.venue || {};
        const isShared = this.bookingForm.bookingType === "SHARED";
        const isMulti = this.isMultiSlot;
        let timeInfo = "";
        let timeDetails = [];
        let totalPrice = 0;
        let actualPrice = 0;
        if (isMulti && ((_a = this.selectedSlots) == null ? void 0 : _a.length) > 0) {
          const sortedSlots = [...this.selectedSlots].sort((a, b) => {
            const timeA = a.startTime.split(":").map(Number);
            const timeB = b.startTime.split(":").map(Number);
            return timeA[0] * 60 + timeA[1] - (timeB[0] * 60 + timeB[1]);
          });
          timeDetails = sortedSlots.map((slot) => `${slot.startTime}-${slot.endTime}`);
          timeInfo = `${this.selectedDate} ${timeDetails.join("、")}`;
          totalPrice = this.totalCost;
          actualPrice = isShared ? totalPrice / 2 : totalPrice;
        } else if (this.selectedSlot) {
          timeDetails = [`${this.selectedSlot.startTime}-${this.selectedSlot.endTime}`];
          timeInfo = `${this.selectedDate} ${timeDetails[0]}`;
          totalPrice = this.totalCost;
          actualPrice = isShared ? totalPrice / 2 : totalPrice;
        }
        return {
          venueName: venue.name || "未知场馆",
          timeInfo,
          timeDetails,
          bookingType: isShared ? "拼场预约" : "包场预约",
          totalPrice,
          actualPrice,
          isShared,
          teamName: this.bookingForm.teamName,
          contactInfo: this.bookingForm.contactInfo,
          description: this.bookingForm.description
        };
      } catch (error) {
        console.error("[BookingCreate] ❌ 构建确认信息失败:", error);
        return {
          venueName: "未知场馆",
          timeInfo: "",
          timeDetails: [],
          bookingType: "包场预约",
          totalPrice: 0,
          actualPrice: 0,
          isShared: false,
          teamName: "",
          contactInfo: "",
          description: ""
        };
      }
    },
    // 🎯 取消确认弹窗
    cancelConfirmDialog() {
      try {
        console.log("[BookingCreate] 🚫 用户取消确认弹窗");
        if (this.confirmDialogResolve) {
          this.confirmDialogResolve(false);
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 取消确认弹窗异常:", error);
      } finally {
        this.showConfirmDialog = false;
        this.confirmDialogData = null;
        this.confirmDialogResolve = null;
      }
    },
    // 🎯 确认弹窗操作
    confirmDialogAction() {
      try {
        console.log("[BookingCreate] ✅ 用户确认弹窗操作");
        if (this.confirmDialogResolve) {
          this.confirmDialogResolve(true);
        }
      } catch (error) {
        console.error("[BookingCreate] ❌ 确认弹窗操作异常:", error);
      } finally {
        this.showConfirmDialog = false;
        this.confirmDialogData = null;
        this.confirmDialogResolve = null;
      }
    }
  },
  watch: {
    // 移除了showTimeSelector的watch监听器，因为该变量未在data中定义
    // 这可能是导致弹窗自动打开的原因
  },
  mounted() {
    this.showConfirmDialog = false;
    this.confirmDialogData = null;
    this.confirmDialogResolve = null;
    this.venueStore = stores_venue.useVenueStore();
    this.bookingStore = stores_booking.useBookingStore();
    this.userStore = stores_user.useUserStore();
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    this.venueId = currentPage.options.id || currentPage.options.venueId;
    if (!this.venueId) {
      console.error("未获取到场馆ID");
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "error"
      });
      return;
    }
    this.selectedDate = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
    this.loadVenueDetail();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t;
  return common_vendor.e({
    a: $options.venue
  }, $options.venue ? {
    b: $options.venue.image || "https://via.placeholder.com/400x200?text=场馆图片",
    c: common_vendor.t($options.venue.name),
    d: common_vendor.t($options.venue.location),
    e: common_vendor.t($options.venue.price)
  } : {}, {
    f: common_vendor.t($data.bookingForm.bookingType === "EXCLUSIVE" ? "独享预约" : "拼场预约"),
    g: $options.venue && $options.venue.supportSharing && $data.bookingForm.bookingType === "SHARED"
  }, $options.venue && $options.venue.supportSharing && $data.bookingForm.bookingType === "SHARED" ? {} : {}, {
    h: common_vendor.t($options.formatDateTime($data.selectedDate)),
    i: $data.bookingForm.bookingType === "SHARED"
  }, $data.bookingForm.bookingType === "SHARED" ? {
    j: $data.bookingForm.teamName,
    k: common_vendor.o(($event) => $data.bookingForm.teamName = $event.detail.value),
    l: $data.bookingForm.contactInfo,
    m: common_vendor.o(($event) => $data.bookingForm.contactInfo = $event.detail.value)
  } : {}, {
    n: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? "拼场说明" : "备注信息"),
    o: $data.bookingForm.bookingType === "SHARED" ? "球队实力中等，出汗局" : "请输入备注信息（可选）",
    p: $data.bookingForm.description,
    q: common_vendor.o(($event) => $data.bookingForm.description = $event.detail.value),
    r: $data.selectedSlots && $data.selectedSlots.length > 0
  }, $data.selectedSlots && $data.selectedSlots.length > 0 ? {
    s: common_vendor.f($data.selectedSlots, (slot, index, i0) => {
      return {
        a: common_vendor.t(slot.startTime),
        b: common_vendor.t(slot.endTime),
        c: common_vendor.t($options.getSlotPrice(slot)),
        d: index
      };
    })
  } : $data.selectedSlot ? {
    v: common_vendor.t($data.selectedSlot.startTime),
    w: common_vendor.t($data.selectedSlot.endTime),
    x: common_vendor.t($options.getSlotPrice($data.selectedSlot))
  } : {
    y: common_vendor.t(((_a = $options.venue) == null ? void 0 : _a.price) || 0)
  }, {
    t: $data.selectedSlot,
    z: $data.bookingForm.bookingType === "SHARED"
  }, $data.bookingForm.bookingType === "SHARED" ? {
    A: common_vendor.t(($options.totalCost / 2).toFixed(2)),
    B: common_vendor.t($options.totalCost),
    C: common_vendor.t(($options.totalCost / 2).toFixed(2))
  } : {
    D: common_vendor.t($options.totalCost)
  }, {
    E: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? "实付金额：" : "总费用："),
    F: common_vendor.t($data.bookingForm.bookingType === "SHARED" ? ($options.totalCost / 2).toFixed(2) : $options.totalCost.toFixed(2)),
    G: $data.bookingForm.bookingType === "SHARED" ? 1 : "",
    H: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    I: !$options.canConfirm,
    J: common_vendor.o((...args) => $options.confirmBooking && $options.confirmBooking(...args)),
    K: $data.showConfirmDialog
  }, $data.showConfirmDialog ? common_vendor.e({
    L: common_vendor.t((_b = $data.confirmDialogData) == null ? void 0 : _b.venueName),
    M: common_vendor.t((_c = $data.confirmDialogData) == null ? void 0 : _c.timeInfo),
    N: ((_e = (_d = $data.confirmDialogData) == null ? void 0 : _d.timeDetails) == null ? void 0 : _e.length) > 0
  }, ((_g = (_f = $data.confirmDialogData) == null ? void 0 : _f.timeDetails) == null ? void 0 : _g.length) > 0 ? {
    O: common_vendor.f($data.confirmDialogData.timeDetails, (detail, index, i0) => {
      return {
        a: common_vendor.t(detail),
        b: index
      };
    })
  } : {}, {
    P: common_vendor.t((_h = $data.confirmDialogData) == null ? void 0 : _h.bookingType),
    Q: (_i = $data.confirmDialogData) == null ? void 0 : _i.isShared
  }, ((_j = $data.confirmDialogData) == null ? void 0 : _j.isShared) ? {
    R: common_vendor.t((_k = $data.confirmDialogData) == null ? void 0 : _k.teamName),
    S: common_vendor.t((_l = $data.confirmDialogData) == null ? void 0 : _l.contactInfo)
  } : {}, {
    T: (_m = $data.confirmDialogData) == null ? void 0 : _m.description
  }, ((_n = $data.confirmDialogData) == null ? void 0 : _n.description) ? {
    U: common_vendor.t((_o = $data.confirmDialogData) == null ? void 0 : _o.description)
  } : {}, {
    V: common_vendor.t(((_p = $data.confirmDialogData) == null ? void 0 : _p.isShared) ? "实付金额：" : "总费用："),
    W: common_vendor.t((_q = $data.confirmDialogData) == null ? void 0 : _q.actualPrice),
    X: (_r = $data.confirmDialogData) == null ? void 0 : _r.isShared
  }, ((_s = $data.confirmDialogData) == null ? void 0 : _s.isShared) ? {
    Y: common_vendor.t((_t = $data.confirmDialogData) == null ? void 0 : _t.totalPrice)
  } : {}, {
    Z: common_vendor.o((...args) => $options.cancelConfirmDialog && $options.cancelConfirmDialog(...args)),
    aa: common_vendor.o((...args) => $options.confirmDialogAction && $options.confirmDialogAction(...args)),
    ab: common_vendor.o(() => {
    }),
    ac: common_vendor.o((...args) => $options.cancelConfirmDialog && $options.cancelConfirmDialog(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8ad5571f"]]);
wx.createPage(MiniProgramPage);
