
.test-container.data-v-50fe2e62 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.test-header.data-v-50fe2e62 {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
}
.test-title.data-v-50fe2e62 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.test-subtitle.data-v-50fe2e62 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.test-section.data-v-50fe2e62 {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
}
.section-title.data-v-50fe2e62 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.test-item.data-v-50fe2e62 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}
.test-label.data-v-50fe2e62 {
  font-size: 28rpx;
  color: #666;
}
.test-value.data-v-50fe2e62 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.test-button.data-v-50fe2e62 {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.clear-btn.data-v-50fe2e62 {
  background-color: #ff3b30;
}
.log-container.data-v-50fe2e62 {
  height: 400rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 10rpx;
  background-color: #f9f9f9;
}
.log-item.data-v-50fe2e62 {
  margin-bottom: 10rpx;
}
.log-text.data-v-50fe2e62 {
  font-size: 24rpx;
  line-height: 1.4;
  display: block;
}
.log-text.info.data-v-50fe2e62 {
  color: #333;
}
.log-text.success.data-v-50fe2e62 {
  color: #34c759;
  font-weight: bold;
}
.log-text.warning.data-v-50fe2e62 {
  color: #ff9500;
  font-weight: bold;
}
.log-text.error.data-v-50fe2e62 {
  color: #ff3b30;
  font-weight: bold;
}
