"use strict";
function debugTimeSlotExpired(selectedDate, timeSlots, currentTime = /* @__PURE__ */ new Date()) {
  console.log("🔧 [DEBUG] 开始调试时间段过期问题");
  const currentDateStr = currentTime.toISOString().split("T")[0];
  const currentTimeStr = currentTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });
  console.log("🔧 [DEBUG] 基础信息:", {
    selectedDate,
    currentDate: currentDateStr,
    currentTime: currentTimeStr,
    currentTimeISO: currentTime.toISOString(),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timezoneOffset: currentTime.getTimezoneOffset()
  });
  const isToday = selectedDate === currentDateStr;
  const isFutureDate = selectedDate > currentDateStr;
  const isPastDate = selectedDate < currentDateStr;
  console.log("🔧 [DEBUG] 日期比较:", {
    isToday,
    isFutureDate,
    isPastDate,
    dateComparison: selectedDate === currentDateStr ? "same" : selectedDate > currentDateStr ? "future" : "past"
  });
  const analysis = {
    totalSlots: timeSlots.length,
    availableSlots: 0,
    expiredSlots: 0,
    bookedSlots: 0,
    otherSlots: 0,
    problemSlots: []
  };
  timeSlots.forEach((slot, index) => {
    console.log(`🔧 [DEBUG] 分析时间段 ${index + 1}:`, {
      id: slot.id,
      date: slot.date,
      timeRange: `${slot.startTime}-${slot.endTime}`,
      status: slot.status,
      originalData: slot
    });
    switch (slot.status) {
      case "AVAILABLE":
        analysis.availableSlots++;
        break;
      case "EXPIRED":
        analysis.expiredSlots++;
        if (isFutureDate) {
          console.error("🚨 [DEBUG] 发现问题：未来日期的时间段被标记为EXPIRED!", {
            slotId: slot.id,
            slotDate: slot.date,
            selectedDate,
            timeRange: `${slot.startTime}-${slot.endTime}`,
            status: slot.status
          });
          analysis.problemSlots.push({
            ...slot,
            problem: "未来日期被标记为过期",
            shouldBeStatus: "AVAILABLE"
          });
        } else if (isToday) {
          try {
            const slotEndDateTime = /* @__PURE__ */ new Date();
            const [endHour, endMinute] = slot.endTime.split(":").map(Number);
            slotEndDateTime.setFullYear(parseInt(selectedDate.split("-")[0]));
            slotEndDateTime.setMonth(parseInt(selectedDate.split("-")[1]) - 1);
            slotEndDateTime.setDate(parseInt(selectedDate.split("-")[2]));
            slotEndDateTime.setHours(endHour, endMinute, 0, 0);
            const shouldBeExpired = currentTime > slotEndDateTime;
            console.log(`🔧 [DEBUG] 今日时间段过期检查:`, {
              slotId: slot.id,
              timeRange: `${slot.startTime}-${slot.endTime}`,
              slotEndDateTime: slotEndDateTime.toISOString(),
              currentTime: currentTime.toISOString(),
              shouldBeExpired,
              actualStatus: slot.status,
              timeDiffMinutes: Math.round((slotEndDateTime - currentTime) / (1e3 * 60))
            });
            if (!shouldBeExpired) {
              console.error("🚨 [DEBUG] 发现问题：今日时间段被错误标记为EXPIRED!", {
                slotId: slot.id,
                timeRange: `${slot.startTime}-${slot.endTime}`,
                slotEndTime: slotEndDateTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }),
                currentTime: currentTimeStr,
                shouldBeExpired
              });
              analysis.problemSlots.push({
                ...slot,
                problem: "今日时间段被错误标记为过期",
                shouldBeStatus: "AVAILABLE",
                slotEndTime: slotEndDateTime.toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }),
                timeDiffMinutes: Math.round((slotEndDateTime - currentTime) / (1e3 * 60))
              });
            }
          } catch (error) {
            console.error("🚨 [DEBUG] 时间段过期检查出错:", error);
          }
        }
        break;
      case "BOOKED":
      case "RESERVED":
        analysis.bookedSlots++;
        break;
      default:
        analysis.otherSlots++;
    }
  });
  console.log("🔧 [DEBUG] 时间段状态统计:", analysis);
  if (analysis.problemSlots.length > 0) {
    console.error("🚨 [DEBUG] 发现问题时间段:", analysis.problemSlots);
    const suggestions = [];
    if (isFutureDate && analysis.expiredSlots > 0) {
      suggestions.push("未来日期的时间段不应该被标记为EXPIRED，请检查后端定时任务逻辑");
    }
    if (isToday && analysis.problemSlots.some((slot) => slot.problem.includes("错误标记"))) {
      suggestions.push("今日时间段的过期判断逻辑有问题，请检查时间计算逻辑");
    }
    console.log("🔧 [DEBUG] 修复建议:", suggestions);
  } else {
    console.log("✅ [DEBUG] 未发现明显的时间段状态问题");
  }
  return {
    analysis,
    hasProblems: analysis.problemSlots.length > 0,
    problemSlots: analysis.problemSlots,
    debugInfo: {
      selectedDate,
      currentDate: currentDateStr,
      currentTime: currentTimeStr,
      isToday,
      isFutureDate,
      isPastDate
    }
  };
}
function quickDebugCurrentPage() {
  console.log("🔧 [DEBUG] 快速调试当前页面时间段问题");
  const currentInstance = getCurrentInstance();
  if (currentInstance) {
    const { selectedDate, timeSlots } = currentInstance.ctx;
    if (selectedDate && timeSlots) {
      return debugTimeSlotExpired(selectedDate, timeSlots);
    }
  }
  console.warn("🔧 [DEBUG] 无法获取当前页面的时间段数据");
  return null;
}
if (typeof window !== "undefined") {
  window.debugTimeSlotExpired = debugTimeSlotExpired;
  window.quickDebugCurrentPage = quickDebugCurrentPage;
  console.log("🔧 [DEBUG] 调试函数已暴露到全局: window.debugTimeSlotExpired, window.quickDebugCurrentPage");
}
