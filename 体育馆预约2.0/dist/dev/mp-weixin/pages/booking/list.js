"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_booking = require("../../stores/booking.js");
const stores_user = require("../../stores/user.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_countdown = require("../../utils/countdown.js");
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  _component_uni_popup();
}
if (!Math) {
  CountdownTimer();
}
const CountdownTimer = () => "../../components/CountdownTimer.js";
const _sfc_main = {
  __name: "list",
  setup(__props) {
    const bookingStore = stores_booking.useBookingStore();
    stores_user.useUserStore();
    const selectedStatus = common_vendor.ref("all");
    const statusOptions = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "待支付", value: "PENDING" },
      { label: "已支付", value: "PAID" },
      { label: "已确认", value: "CONFIRMED" },
      { label: "已核销", value: "VERIFIED" },
      { label: "已完成", value: "COMPLETED" },
      { label: "已取消", value: "CANCELLED" },
      { label: "已过期", value: "EXPIRED" },
      // 拼场相关状态
      { label: "开放中", value: "OPEN" },
      { label: "等待对方支付", value: "APPROVED_PENDING_PAYMENT" },
      { label: "拼场成功", value: "SHARING_SUCCESS" }
    ]);
    const currentBookingId = common_vendor.ref(null);
    const cancelPopup = common_vendor.ref(null);
    const internalPopupOpened = common_vendor.ref(false);
    common_vendor.ref({ top: 0, left: 0 });
    const _popupRef = common_vendor.ref(null);
    const showCancelModal = (bookingId) => {
      currentBookingId.value = bookingId;
      const debugEnabled = false;
      try {
        if (cancelPopup.value && typeof cancelPopup.value.open === "function") {
          const popup = Array.isArray(cancelPopup.value) ? cancelPopup.value[0] : cancelPopup.value;
          if (popup && typeof popup.open === "function") {
            popup.open();
            internalPopupOpened.value = true;
            if (debugEnabled)
              ;
            return;
          }
        }
        if (_popupRef.value && typeof _popupRef.value.open === "function") {
          _popupRef.value.open();
          internalPopupOpened.value = true;
          if (debugEnabled)
            ;
          return;
        }
        if (typeof common_vendor.index !== "undefined") {
          try {
            const windowInfo = common_vendor.index.getWindowInfo ? common_vendor.index.getWindowInfo() : common_vendor.index.getSystemInfoSync();
            const deviceInfo = common_vendor.index.getDeviceInfo ? common_vendor.index.getDeviceInfo() : {};
            const appInfo = common_vendor.index.getAppBaseInfo ? common_vendor.index.getAppBaseInfo() : {};
            if (windowInfo.platform === "devtools" || appInfo.uniPlatform === "mp-weixin" || deviceInfo.platform === "devtools") {
              const currentInstance = common_vendor.getCurrentInstance();
              if (currentInstance && currentInstance.ctx && currentInstance.ctx.$scope && typeof currentInstance.ctx.$scope.selectComponent === "function") {
                const popup = currentInstance.ctx.$scope.selectComponent("#cancelPopup");
                if (popup && typeof popup.open === "function") {
                  popup.open();
                  internalPopupOpened.value = true;
                  if (debugEnabled)
                    ;
                  return;
                }
                if (currentInstance.ctx.$scope.selectAllComponents) {
                  const popups = currentInstance.ctx.$scope.selectAllComponents("uni-popup");
                  if (popups && popups.length > 0) {
                    const targetPopup = popups.find((p) => p.data && p.data.type === "center");
                    if (targetPopup && typeof targetPopup.open === "function") {
                      targetPopup.open();
                      internalPopupOpened.value = true;
                      _popupRef.value = targetPopup;
                      if (debugEnabled)
                        ;
                      return;
                    }
                  }
                }
              }
            }
          } catch (e) {
            if (debugEnabled)
              ;
          }
        }
        setTimeout(() => {
          if (cancelPopup.value && typeof cancelPopup.value.open === "function") {
            const popup = Array.isArray(cancelPopup.value) ? cancelPopup.value[0] : cancelPopup.value;
            if (popup && typeof popup.open === "function") {
              popup.open();
              internalPopupOpened.value = true;
              if (debugEnabled)
                ;
              return;
            }
          }
          try {
            const popupEl = document.querySelector(".uni-popup");
            if (popupEl) {
              popupEl.style.display = "flex";
              popupEl.style.visibility = "visible";
              internalPopupOpened.value = true;
              if (debugEnabled)
                ;
            }
          } catch (domError) {
            if (debugEnabled)
              ;
          }
        }, 100);
        if (debugEnabled)
          ;
      } catch (error) {
      }
    };
    const bookingList = common_vendor.computed(() => bookingStore.bookingListGetter);
    const loading = common_vendor.computed(() => bookingStore.isLoading);
    const lastRequestStatus = common_vendor.ref("");
    const lastError = common_vendor.ref("");
    const pagination = common_vendor.computed(() => {
      return bookingStore.getPagination;
    });
    const hasMore = common_vendor.computed(() => {
      return pagination.value.current < pagination.value.totalPages;
    });
    const filteredBookings = common_vendor.computed(() => {
      const bookings = bookingList.value || [];
      console.log("[BookingList] filteredBookings计算中...");
      console.log("[BookingList] 原始数据:", bookings.length, "条");
      console.log("[BookingList] 选中状态:", selectedStatus.value);
      if (selectedStatus.value === "all") {
        console.log("[BookingList] 返回全部数据:", bookings.length, "条");
        return bookings;
      }
      const filtered = bookings.filter(
        (booking) => {
          console.log("[BookingList] 筛选订单:", booking.id, "状态:", booking.status, "匹配:", booking.status === selectedStatus.value);
          return booking.status === selectedStatus.value;
        }
      );
      console.log("[BookingList] 筛选后数据:", filtered.length, "条");
      return filtered;
    });
    const initData = async () => {
      try {
        lastRequestStatus.value = "请求中...";
        lastError.value = "无";
        await bookingStore.getUserBookings({ page: 1, pageSize: 10 });
        lastRequestStatus.value = "请求成功";
      } catch (error) {
        lastRequestStatus.value = "请求失败";
        lastError.value = error.message || "获取预约列表失败";
        console.error("初始化数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取预约列表失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const refreshData = async () => {
      try {
        lastRequestStatus.value = "刷新中...";
        lastError.value = "无";
        await bookingStore.getUserBookings({
          page: 1,
          pageSize: 10,
          refresh: true,
          timestamp: Date.now()
        });
        lastRequestStatus.value = "刷新成功";
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        lastRequestStatus.value = "刷新失败";
        lastError.value = error.message || "刷新数据失败";
        common_vendor.index.stopPullDownRefresh();
        console.error("刷新数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "刷新数据失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const loadMore = async () => {
      if (loading.value || !hasMore.value)
        return;
      try {
        const nextPage = pagination.value.current + 1;
        await bookingStore.getUserBookings({ page: nextPage, pageSize: 10 });
      } catch (error) {
        console.error("加载更多失败:", error);
      }
    };
    const selectStatus = (status) => {
      selectedStatus.value = status;
    };
    const navigateToDetail = (bookingId) => {
      common_vendor.index.navigateTo({
        url: `/pages/booking/detail?id=${bookingId}`
      });
    };
    const closeCancelModal = () => {
      const debugEnabled = false;
      try {
        if (cancelPopup.value && typeof cancelPopup.value.close === "function") {
          const popup = Array.isArray(cancelPopup.value) ? cancelPopup.value[0] : cancelPopup.value;
          if (popup && typeof popup.close === "function") {
            popup.close();
            internalPopupOpened.value = false;
            if (debugEnabled)
              ;
            currentBookingId.value = null;
            return;
          }
        }
        if (_popupRef.value && typeof _popupRef.value.close === "function") {
          _popupRef.value.close();
          internalPopupOpened.value = false;
          if (debugEnabled)
            ;
          currentBookingId.value = null;
          return;
        }
        if (typeof common_vendor.index !== "undefined") {
          try {
            const windowInfo = common_vendor.index.getWindowInfo ? common_vendor.index.getWindowInfo() : common_vendor.index.getSystemInfoSync();
            const deviceInfo = common_vendor.index.getDeviceInfo ? common_vendor.index.getDeviceInfo() : {};
            const appInfo = common_vendor.index.getAppBaseInfo ? common_vendor.index.getAppBaseInfo() : {};
            if (windowInfo.platform === "devtools" || appInfo.uniPlatform === "mp-weixin" || deviceInfo.platform === "devtools") {
              const currentInstance = common_vendor.getCurrentInstance();
              if (currentInstance && currentInstance.ctx && currentInstance.ctx.$scope && typeof currentInstance.ctx.$scope.selectComponent === "function") {
                const popup = currentInstance.ctx.$scope.selectComponent("#cancelPopup");
                if (popup && typeof popup.close === "function") {
                  popup.close();
                  internalPopupOpened.value = false;
                  if (debugEnabled)
                    ;
                  currentBookingId.value = null;
                  return;
                }
                if (currentInstance.ctx.$scope.selectAllComponents) {
                  const popups = currentInstance.ctx.$scope.selectAllComponents("uni-popup");
                  if (popups && popups.length > 0) {
                    const targetPopup = popups.find((p) => p.data && p.data.type === "center");
                    if (targetPopup && typeof targetPopup.close === "function") {
                      targetPopup.close();
                      internalPopupOpened.value = false;
                      if (debugEnabled)
                        ;
                      currentBookingId.value = null;
                      return;
                    }
                  }
                }
              }
            }
          } catch (e) {
            if (debugEnabled)
              ;
          }
        }
        setTimeout(() => {
          if (cancelPopup.value && typeof cancelPopup.value.close === "function") {
            const popup = Array.isArray(cancelPopup.value) ? cancelPopup.value[0] : cancelPopup.value;
            if (popup && typeof popup.close === "function") {
              popup.close();
              internalPopupOpened.value = false;
              if (debugEnabled)
                ;
              return;
            }
          }
          try {
            const popupEl = document.querySelector(".uni-popup");
            if (popupEl) {
              popupEl.style.display = "none";
              popupEl.style.visibility = "hidden";
              internalPopupOpened.value = false;
              if (debugEnabled)
                ;
            }
          } catch (domError) {
            if (debugEnabled)
              ;
          }
          internalPopupOpened.value = false;
        }, 100);
        if (debugEnabled)
          ;
      } catch (error) {
        internalPopupOpened.value = false;
      }
      currentBookingId.value = null;
    };
    const confirmCancel = async () => {
      if (!currentBookingId.value) {
        console.error("No booking ID selected for cancellation");
        return;
      }
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await bookingStore.cancelBooking(currentBookingId.value);
        common_vendor.index.hideLoading();
        closeCancelModal();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        await initData();
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("取消预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    };
    const reviewVenue = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/venue/review?venueId=${booking.venueId}&bookingId=${booking.id}`
      });
    };
    const rebookVenue = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/venue/detail?id=${booking.venueId}`
      });
    };
    const navigateToVenueList = () => {
      common_vendor.index.switchTab({
        url: "/pages/venue/list"
      });
    };
    const formatCreateTime = (datetime) => {
      return utils_helpers.formatTime(datetime, "YYYY-MM-DD HH:mm");
    };
    const getStatusClass = (status) => {
      const statusMap = {
        // 基础状态样式
        "PENDING": "status-pending",
        "PAID": "status-paid",
        "CONFIRMED": "status-confirmed",
        "VERIFIED": "status-verified",
        "COMPLETED": "status-completed",
        "CANCELLED": "status-cancelled",
        "EXPIRED": "status-expired",
        // 拼场状态样式
        "OPEN": "status-open",
        "APPROVED_PENDING_PAYMENT": "status-approved-pending-payment",
        "SHARING_SUCCESS": "status-sharing-success",
        "PENDING_FULL": "status-pending-full",
        "FULL": "status-full"
      };
      return statusMap[status] || "status-pending";
    };
    const getStatusText = (status) => {
      const statusMap = {
        // 基础状态（所有订单通用）
        "PENDING": "待支付",
        "PAID": "已支付",
        "CONFIRMED": "已确认",
        "VERIFIED": "已核销",
        "COMPLETED": "已完成",
        "CANCELLED": "已取消",
        "EXPIRED": "已过期",
        // 拼场订单特有状态
        "OPEN": "开放中(1/2)",
        "APPROVED_PENDING_PAYMENT": "等待对方支付",
        "SHARING_SUCCESS": "拼场成功(2人)",
        "PENDING_FULL": "待满员",
        "FULL": "已满员(2/2)"
      };
      return statusMap[status] || "待支付";
    };
    const formatTimeRange = (booking) => {
      const bookingId = typeof booking.id === "string" ? parseInt(booking.id) : booking.id;
      const isVirtual = bookingId < 0;
      if (isVirtual) {
        const startTime = booking.startTime;
        const endTime = booking.endTime;
        console.log("虚拟订单时间显示 - startTime:", startTime, "endTime:", endTime);
        if (!startTime)
          return "时间待定";
        try {
          const startTimeStr = startTime;
          const endTimeStr = endTime;
          if (endTimeStr) {
            return `${startTimeStr} - ${endTimeStr}`;
          } else {
            return startTimeStr;
          }
        } catch (error) {
          console.error("虚拟订单时间格式化错误:", error);
          return "时间待定";
        }
      } else {
        const startTime = booking.startTime || booking.bookingStartTime || booking.timeSlotStartTime;
        const endTime = booking.endTime || booking.bookingEndTime || booking.timeSlotEndTime;
        const timeSlotCount = booking.timeSlotCount || booking.slotCount || 1;
        console.log("普通订单时间显示 - booking:", {
          id: booking.id,
          type: booking.bookingType || booking.type,
          startTime,
          endTime,
          timeSlotCount,
          原始数据: {
            startTime: booking.startTime,
            endTime: booking.endTime,
            bookingStartTime: booking.bookingStartTime,
            bookingEndTime: booking.bookingEndTime,
            timeSlotStartTime: booking.timeSlotStartTime,
            timeSlotEndTime: booking.timeSlotEndTime
          }
        });
        if (!startTime || !endTime) {
          console.warn("普通订单时间字段缺失:", booking);
          return "时间待定";
        }
        const formatTime = (timeStr) => {
          if (!timeStr)
            return "";
          if (timeStr.includes("T")) {
            return timeStr.split("T")[1].substring(0, 5);
          }
          if (timeStr.length > 5 && timeStr.includes(":")) {
            return timeStr.substring(0, 5);
          }
          return timeStr;
        };
        const formattedStart = formatTime(startTime);
        const formattedEnd = formatTime(endTime);
        if (timeSlotCount > 1) {
          return `${formattedStart} - ${formattedEnd} (${timeSlotCount}个时段)`;
        }
        return `${formattedStart} - ${formattedEnd}`;
      }
    };
    const getBookingTypeText = (bookingType) => {
      const typeMap = {
        "EXCLUSIVE": "包场",
        "SHARED": "拼场"
      };
      return bookingType ? typeMap[bookingType] || "普通" : "普通";
    };
    const getBookingTypeClass = (bookingType) => {
      const classMap = {
        "EXCLUSIVE": "tag-exclusive",
        "SHARED": "tag-shared"
      };
      return bookingType ? classMap[bookingType] || "tag-default" : "tag-default";
    };
    const isVirtualOrder = (booking) => {
      if (!booking)
        return false;
      const bookingId = typeof booking.id === "string" ? parseInt(booking.id) : booking.id;
      return bookingId < 0;
    };
    const getBookingPrice = (booking) => {
      if (!booking)
        return "0.00";
      const virtualOrder = isVirtualOrder(booking);
      let price;
      if (virtualOrder) {
        price = booking.paymentAmount || 0;
      } else {
        price = booking.totalPrice || 0;
      }
      return price.toFixed(2);
    };
    const formatBookingDate = (booking) => {
      if (!booking)
        return "";
      const virtualOrder = isVirtualOrder(booking);
      if (virtualOrder) {
        const bookingTime = booking.bookingTime;
        if (!bookingTime)
          return "";
        try {
          let dateTime;
          if (typeof bookingTime === "string") {
            let isoTime = bookingTime;
            if (bookingTime.includes(" ") && !bookingTime.includes("T")) {
              isoTime = bookingTime.replace(" ", "T");
            }
            dateTime = new Date(isoTime);
          } else {
            dateTime = new Date(bookingTime);
          }
          if (isNaN(dateTime.getTime())) {
            console.error("虚拟订单日期格式化错误 - 无效的时间:", bookingTime);
            return "";
          }
          return dateTime.toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit"
          }).replace(/\//g, "-");
        } catch (error) {
          console.error("虚拟订单日期格式化错误:", error);
          return "";
        }
      } else {
        if (booking.bookingDate) {
          return utils_helpers.formatDate(booking.bookingDate);
        }
        return "";
      }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.$on("sharingDataChanged", onSharingDataChanged);
      common_vendor.index.$on("bookingCreated", onBookingCreated);
      common_vendor.nextTick$1(() => {
        if (cancelPopup.value) {
          _popupRef.value = cancelPopup.value;
          console.log("cancelPopup ref cached:", _popupRef.value);
        } else {
          setTimeout(() => {
            if (cancelPopup.value) {
              _popupRef.value = cancelPopup.value;
              console.log("cancelPopup ref cached (delayed):", _popupRef.value);
            }
          }, 100);
        }
      });
      initData();
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("sharingDataChanged", onSharingDataChanged);
      common_vendor.index.$off("bookingCreated", onBookingCreated);
      currentBookingId.value = null;
      cancelPopup.value = null;
    });
    const onSharingDataChanged = (data) => {
      console.log("预约列表页面：收到拼场数据变化通知:", data);
      initData();
    };
    const onBookingCreated = (data) => {
      console.log("预约列表页面：收到预约创建成功通知:", data);
      refreshData();
    };
    common_vendor.onShow(async () => {
      await refreshData();
    });
    common_vendor.onPullDownRefresh(async () => {
      await refreshData();
    });
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    const onCountdownExpired = (_order) => {
      initData();
    };
    const payOrder = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/payment/index?orderId=${booking.id}&type=booking`
      });
    };
    const viewOrderDetail = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/booking/detail?id=${booking.id}`
      });
    };
    const viewParticipants = (booking) => {
      common_vendor.index.navigateTo({
        url: `/pages/sharing/participants?orderId=${booking.id}`
      });
    };
    const checkinOrder = (_booking) => {
      common_vendor.index.showModal({
        title: "确认签到",
        content: "确认已到达场馆并开始使用？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "签到成功",
              icon: "success"
            });
            initData();
          }
        }
      });
    };
    const completeOrder = (_booking) => {
      common_vendor.index.showModal({
        title: "完成订单",
        content: "确认完成此次预约？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "订单已完成",
              icon: "success"
            });
            initData();
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(statusOptions.value, (status, k0, i0) => {
          return {
            a: common_vendor.t(status.label),
            b: status.value,
            c: selectedStatus.value === status.value ? 1 : "",
            d: common_vendor.o(($event) => selectStatus(status.value), status.value)
          };
        }),
        b: common_vendor.f(filteredBookings.value, (booking, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(booking.venueName || "未知场馆"),
            b: booking.bookingType || booking.type || booking.orderType
          }, booking.bookingType || booking.type || booking.orderType ? {
            c: common_vendor.t(getBookingTypeText(booking.bookingType || booking.type || booking.orderType)),
            d: common_vendor.n(getBookingTypeClass(booking.bookingType || booking.type || booking.orderType))
          } : {}, {
            e: isVirtualOrder(booking)
          }, isVirtualOrder(booking) ? {} : {}, {
            f: common_vendor.t(formatBookingDate(booking)),
            g: common_vendor.t(getStatusText(booking.status)),
            h: common_vendor.n(getStatusClass(booking.status)),
            i: common_vendor.t(formatTimeRange(booking)),
            j: common_vendor.t(booking.venueLocation || "未知地点"),
            k: common_vendor.t(booking.orderNo || (booking.id ? booking.id : "")),
            l: common_vendor.t(formatCreateTime(booking && (booking.createdAt || booking.createTime))),
            m: common_vendor.t(getBookingPrice(booking)),
            n: common_vendor.unref(utils_countdown.shouldShowCountdown)(booking)
          }, common_vendor.unref(utils_countdown.shouldShowCountdown)(booking) ? {
            o: common_vendor.o(onCountdownExpired, `booking-${booking.id}-${index}`),
            p: "afb09895-0-" + i0,
            q: common_vendor.p({
              order: booking,
              label: "自动取消",
              short: true
            })
          } : {}, {
            r: booking.status === "PENDING"
          }, booking.status === "PENDING" ? {
            s: common_vendor.o(($event) => payOrder(booking), `booking-${booking.id}-${index}`),
            t: common_vendor.o(($event) => showCancelModal(booking.id), `booking-${booking.id}-${index}`)
          } : booking.status === "PAID" ? {
            w: common_vendor.o(($event) => viewOrderDetail(booking), `booking-${booking.id}-${index}`),
            x: common_vendor.o(($event) => showCancelModal(booking.id), `booking-${booking.id}-${index}`)
          } : booking.status === "OPEN" || booking.status === "SHARING" || booking.status === "PENDING_FULL" ? {
            z: common_vendor.o(($event) => viewOrderDetail(booking), `booking-${booking.id}-${index}`),
            A: common_vendor.o(($event) => viewParticipants(booking), `booking-${booking.id}-${index}`),
            B: common_vendor.o(($event) => showCancelModal(booking.id), `booking-${booking.id}-${index}`)
          } : booking.status === "SHARING_SUCCESS" || booking.status === "FULL" ? {
            D: common_vendor.o(($event) => viewOrderDetail(booking), `booking-${booking.id}-${index}`),
            E: common_vendor.o(($event) => viewParticipants(booking), `booking-${booking.id}-${index}`)
          } : booking.status === "CONFIRMED" ? {
            G: common_vendor.o(($event) => checkinOrder(), `booking-${booking.id}-${index}`),
            H: common_vendor.o(($event) => showCancelModal(booking.id), `booking-${booking.id}-${index}`)
          } : booking.status === "VERIFIED" ? {
            J: common_vendor.o(($event) => completeOrder(), `booking-${booking.id}-${index}`)
          } : booking.status === "COMPLETED" ? {
            L: common_vendor.o(($event) => reviewVenue(booking), `booking-${booking.id}-${index}`),
            M: common_vendor.o(($event) => rebookVenue(booking), `booking-${booking.id}-${index}`)
          } : booking.status === "CANCELLED" || booking.status === "EXPIRED" ? {
            O: common_vendor.o(($event) => rebookVenue(booking), `booking-${booking.id}-${index}`)
          } : {}, {
            v: booking.status === "PAID",
            y: booking.status === "OPEN" || booking.status === "SHARING" || booking.status === "PENDING_FULL",
            C: booking.status === "SHARING_SUCCESS" || booking.status === "FULL",
            F: booking.status === "CONFIRMED",
            I: booking.status === "VERIFIED",
            K: booking.status === "COMPLETED",
            N: booking.status === "CANCELLED" || booking.status === "EXPIRED",
            P: `booking-${booking.id}-${index}`,
            Q: common_vendor.o(($event) => navigateToDetail(booking.id), `booking-${booking.id}-${index}`)
          });
        }),
        c: filteredBookings.value.length === 0
      }, filteredBookings.value.length === 0 ? {
        d: common_vendor.o(navigateToVenueList)
      } : {}, {
        e: hasMore.value && filteredBookings.value.length > 0
      }, hasMore.value && filteredBookings.value.length > 0 ? {
        f: common_vendor.t(loading.value ? "加载中..." : "加载更多"),
        g: common_vendor.o(loadMore)
      } : {}, {
        h: common_vendor.o(closeCancelModal),
        i: common_vendor.o(confirmCancel),
        j: common_vendor.sr(cancelPopup, "afb09895-1", {
          "k": "cancelPopup"
        }),
        k: internalPopupOpened.value,
        l: common_vendor.p({
          type: "center",
          ["mask-click"]: false
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-afb09895"]]);
wx.createPage(MiniProgramPage);
