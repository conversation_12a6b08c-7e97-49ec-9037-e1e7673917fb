<view class="test-container data-v-7e8424cb"><view class="test-header data-v-7e8424cb"><text class="test-title data-v-7e8424cb">支付问题修复测试</text></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试1: 订单金额显示修复</view><view class="test-item data-v-7e8424cb"><text class="test-label data-v-7e8424cb">测试订单ID:</text><input placeholder="输入订单ID" class="test-input data-v-7e8424cb" value="{{a}}" bindinput="{{b}}"/></view><button bindtap="{{c}}" class="test-button data-v-7e8424cb">测试订单金额计算</button><view wx:if="{{d}}" class="test-result data-v-7e8424cb"><text class="result-title data-v-7e8424cb">测试结果:</text><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">原始价格: ¥{{e}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">计算价格: ¥{{f}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">计算方法: {{g}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">建议: {{h}}</text></view></view></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试2: 时间段刷新修复</view><view class="test-item data-v-7e8424cb"><text class="test-label data-v-7e8424cb">场馆ID:</text><input placeholder="输入场馆ID" class="test-input data-v-7e8424cb" value="{{i}}" bindinput="{{j}}"/></view><view class="test-item data-v-7e8424cb"><text class="test-label data-v-7e8424cb">日期:</text><input placeholder="YYYY-MM-DD" class="test-input data-v-7e8424cb" value="{{k}}" bindinput="{{l}}"/></view><button bindtap="{{m}}" class="test-button data-v-7e8424cb">测试时间段刷新</button><view wx:if="{{n}}" class="test-result data-v-7e8424cb"><text class="result-title data-v-7e8424cb">刷新前状态:</text><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">时间段数量: {{o}}</text></view><text class="result-title data-v-7e8424cb">刷新后状态:</text><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">时间段数量: {{p}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">刷新成功: {{q}}</text></view></view></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试3: 价格传递调试</view><view class="test-item data-v-7e8424cb"><text class="test-label data-v-7e8424cb">测试价格:</text><input placeholder="输入价格" class="test-input data-v-7e8424cb" value="{{r}}" bindinput="{{s}}"/></view><button bindtap="{{t}}" class="test-button data-v-7e8424cb">测试价格传递</button><view wx:if="{{v}}" class="test-result data-v-7e8424cb"><text class="result-title data-v-7e8424cb">价格传递测试结果:</text><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">成功: {{w}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">原始价格: {{x}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">最终价格: {{y}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">价格保持: {{z}}</text></view><view wx:if="{{A}}" class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">问题: {{B}}</text></view></view></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试4: 深度问题验证</view><button bindtap="{{C}}" class="test-button data-v-7e8424cb">深度验证修复效果</button><view wx:if="{{D}}" class="test-result data-v-7e8424cb"><text class="result-title data-v-7e8424cb">深度验证结果:</text><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">总体成功: {{E}}</text></view><view class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">总结: {{F}}</text></view><view wx:if="{{G}}" class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">价格传递: {{H}}</text></view><view wx:if="{{I}}" class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">时间段刷新: {{J}}</text></view></view></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试5: 创建预约流程测试</view><button bindtap="{{K}}" class="test-button data-v-7e8424cb">测试完整预约流程</button><view wx:if="{{L}}" class="test-result data-v-7e8424cb"><text class="result-title data-v-7e8424cb">流程测试结果:</text><view wx:for="{{M}}" wx:for-item="step" wx:key="d" class="result-item data-v-7e8424cb"><text class="data-v-7e8424cb">{{step.a}}: {{step.b}} {{step.c}}</text></view></view></view><view class="test-section data-v-7e8424cb"><view class="section-title data-v-7e8424cb">测试日志</view><view class="log-container data-v-7e8424cb"><text wx:for="{{N}}" wx:for-item="log" wx:key="b" class="log-item data-v-7e8424cb">{{log.a}}</text></view></view></view>