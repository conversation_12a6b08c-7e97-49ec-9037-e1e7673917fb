"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_sharing = require("../../stores/sharing.js");
const stores_user = require("../../stores/user.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "SharingRequests",
  data() {
    return {
      sharingStore: null,
      userStore: null,
      currentFilter: "all",
      error: "",
      cancelTarget: null,
      requests: [],
      filterTabs: [
        { label: "全部", value: "all", count: 0 },
        { label: "待处理", value: "pending", count: 0 },
        { label: "待支付", value: "approved_pending_payment", count: 0 },
        { label: "已完成", value: "approved", count: 0 },
        { label: "已拒绝", value: "rejected", count: 0 },
        { label: "已超时", value: "timeout_cancelled", count: 0 }
      ],
      // 缓存优化相关字段
      lastRefreshTime: 0,
      cacheTimeout: 3e4,
      // 30秒缓存
      isRefreshing: false,
      // 弹窗状态控制
      internalCancelPopupOpened: false,
      cancelPopupPosition: "",
      _cancelPopupRef: null
    };
  },
  computed: {
    loading() {
      var _a;
      return ((_a = this.sharingStore) == null ? void 0 : _a.isLoading) || false;
    },
    userInfo() {
      var _a;
      return ((_a = this.userStore) == null ? void 0 : _a.userInfoGetter) || {};
    },
    // 过滤后的申请列表
    filteredRequests() {
      if (this.currentFilter === "all") {
        return this.requests;
      }
      const statusMap = {
        "pending": "PENDING",
        "approved_pending_payment": "APPROVED_PENDING_PAYMENT",
        "approved": "APPROVED",
        "rejected": "REJECTED",
        "timeout_cancelled": "TIMEOUT_CANCELLED"
      };
      return this.requests.filter(
        (request) => request.status === statusMap[this.currentFilter]
      );
    }
  },
  onLoad() {
    this.sharingStore = stores_sharing.useSharingStore();
    this.userStore = stores_user.useUserStore();
    this.internalCancelPopupOpened = false;
    this.cancelPopupPosition = "";
    this._cancelPopupRef = null;
    this.$nextTick(() => {
      if (this.$refs.cancelPopup) {
        this._cancelPopupRef = this.$refs.cancelPopup;
      }
    });
    this.loadRequests();
  },
  onShow() {
    this.loadRequestsWithCache();
  },
  onPullDownRefresh() {
    this.loadRequests().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  onUnload() {
    this._cancelPopupRef = null;
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 🚀 缓存优化的申请列表加载
    async loadRequestsWithCache() {
      if (this.isRefreshing) {
        console.log("拼场申请页面：正在刷新中，跳过重复请求");
        return;
      }
      const now = Date.now();
      if (this.requests.length > 0 && this.lastRefreshTime && now - this.lastRefreshTime < this.cacheTimeout) {
        console.log("拼场申请页面：使用缓存数据，跳过请求");
        return;
      }
      await this.loadRequests();
    },
    // 加载申请列表
    async loadRequests() {
      this.isRefreshing = true;
      try {
        this.error = "";
        console.log("拼场申请页面：开始加载申请列表");
        const response = await this.sharingStore.getSentRequestsList();
        const requests = (response == null ? void 0 : response.data) || (response == null ? void 0 : response.list) || response || [];
        this.requests = Array.isArray(requests) ? requests : [];
        this.updateFilterCounts();
        this.lastRefreshTime = Date.now();
        console.log("拼场申请页面：加载申请列表成功:", this.requests);
        if (this.requests.length > 0) {
          console.log("第一个请求的完整数据结构:", this.requests[0]);
          console.log("第一个请求的sharingId:", this.requests[0].sharingId);
          console.log("第一个请求的所有字段:", Object.keys(this.requests[0]));
        }
      } catch (error) {
        console.error("拼场申请页面：加载申请列表失败:", error);
        this.error = error.message || "加载失败，请重试";
        this.requests = [];
      } finally {
        this.isRefreshing = false;
      }
    },
    // 更新筛选标签计数
    updateFilterCounts() {
      const counts = {
        all: this.requests.length,
        pending: this.requests.filter((r) => r.status === "PENDING").length,
        approved: this.requests.filter((r) => r.status === "APPROVED").length,
        rejected: this.requests.filter((r) => r.status === "REJECTED").length
      };
      this.filterTabs.forEach((tab) => {
        tab.count = counts[tab.value] || 0;
      });
    },
    // 切换筛选
    switchFilter(filter) {
      this.currentFilter = filter;
    },
    // 显示取消确认弹窗
    showCancelConfirm(request) {
      this.cancelTarget = request;
      this.showCancelPopup();
    },
    // 显示取消确认弹窗（兼容微信小程序）
    showCancelPopup() {
      const debugEnabled = false;
      if (this.internalCancelPopupOpened) {
        return;
      }
      const attemptOpen = () => {
        try {
          let windowInfo, deviceInfo, appBaseInfo;
          try {
            windowInfo = common_vendor.index.getWindowInfo();
            deviceInfo = common_vendor.index.getDeviceInfo();
            appBaseInfo = common_vendor.index.getAppBaseInfo();
          } catch (e) {
            if (debugEnabled)
              ;
          }
          if (this.$refs.cancelPopup) {
            const popup = Array.isArray(this.$refs.cancelPopup) ? this.$refs.cancelPopup[0] : this.$refs.cancelPopup;
            if (popup && typeof popup.open === "function") {
              popup.open();
              this.internalCancelPopupOpened = true;
              if (debugEnabled)
                ;
              return true;
            }
          }
          if (this._cancelPopupRef && typeof this._cancelPopupRef.open === "function") {
            this._cancelPopupRef.open();
            this.internalCancelPopupOpened = true;
            if (debugEnabled)
              ;
            return true;
          }
          if ((appBaseInfo == null ? void 0 : appBaseInfo.platform) === "mp-weixin" || (deviceInfo == null ? void 0 : deviceInfo.platform) === "devtools") {
            if (this.$scope && typeof this.$scope.selectComponent === "function") {
              const popup = this.$scope.selectComponent("#cancelPopup");
              if (popup && typeof popup.open === "function") {
                popup.open();
                this.internalCancelPopupOpened = true;
                if (debugEnabled)
                  ;
                return true;
              }
            }
          }
          if (this.$children && this.$children.length > 0) {
            for (let child of this.$children) {
              if (child.$options.name === "UniPopup" || child.$options._componentTag && child.$options._componentTag.includes("uni-popup")) {
                if (typeof child.open === "function") {
                  child.open();
                  this.internalCancelPopupOpened = true;
                  if (debugEnabled)
                    ;
                  return true;
                }
              }
            }
          }
          return false;
        } catch (error) {
          return false;
        }
      };
      if (attemptOpen()) {
        return;
      }
      setTimeout(() => {
        if (attemptOpen()) {
          return;
        }
        try {
          this.internalCancelPopupOpened = true;
          this.cancelPopupPosition = "popup-force-show";
          this.$forceUpdate();
        } catch (e) {
        }
      }, 100);
    },
    // 关闭取消确认弹窗（兼容微信小程序）
    closeCancelPopup() {
      const debugEnabled = false;
      const attemptClose = () => {
        try {
          let windowInfo, deviceInfo, appBaseInfo;
          try {
            windowInfo = common_vendor.index.getWindowInfo();
            deviceInfo = common_vendor.index.getDeviceInfo();
            appBaseInfo = common_vendor.index.getAppBaseInfo();
          } catch (e) {
            if (debugEnabled)
              ;
          }
          if (this.$refs.cancelPopup) {
            const popup = Array.isArray(this.$refs.cancelPopup) ? this.$refs.cancelPopup[0] : this.$refs.cancelPopup;
            if (popup && typeof popup.close === "function") {
              popup.close();
              this.internalCancelPopupOpened = false;
              this.cancelTarget = null;
              if (debugEnabled)
                ;
              return true;
            }
          }
          if (this._cancelPopupRef && typeof this._cancelPopupRef.close === "function") {
            this._cancelPopupRef.close();
            this.internalCancelPopupOpened = false;
            this.cancelTarget = null;
            if (debugEnabled)
              ;
            return true;
          }
          if ((appBaseInfo == null ? void 0 : appBaseInfo.platform) === "mp-weixin" || (deviceInfo == null ? void 0 : deviceInfo.platform) === "devtools") {
            if (this.$scope && typeof this.$scope.selectComponent === "function") {
              const popup = this.$scope.selectComponent("#cancelPopup");
              if (popup && typeof popup.close === "function") {
                popup.close();
                this.internalCancelPopupOpened = false;
                this.cancelTarget = null;
                if (debugEnabled)
                  ;
                return true;
              }
            }
          }
          if (this.$children && this.$children.length > 0) {
            for (let child of this.$children) {
              if (child.$options.name === "UniPopup" || child.$options._componentTag && child.$options._componentTag.includes("uni-popup")) {
                if (typeof child.close === "function") {
                  child.close();
                  this.internalCancelPopupOpened = false;
                  this.cancelTarget = null;
                  if (debugEnabled)
                    ;
                  return true;
                }
              }
            }
          }
          return false;
        } catch (error) {
          return false;
        }
      };
      if (attemptClose()) {
        return;
      }
      setTimeout(() => {
        if (attemptClose()) {
          return;
        }
        try {
          this.internalCancelPopupOpened = false;
          this.cancelPopupPosition = "";
          this.cancelTarget = null;
          this.$forceUpdate();
        } catch (e) {
          this.cancelTarget = null;
        }
      }, 100);
    },
    // 确认取消申请
    async confirmCancel() {
      if (!this.cancelTarget)
        return;
      try {
        common_vendor.index.showLoading({ title: "取消中..." });
        await this.sharingStore.cancelSharingRequest(this.cancelTarget.id);
        const index = this.requests.findIndex((r) => r.id === this.cancelTarget.id);
        if (index > -1) {
          this.requests.splice(index, 1);
        }
        this.updateFilterCounts();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "取消成功",
          icon: "success"
        });
        this.cancelTarget = null;
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("拼场申请页面：取消申请失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      }
    },
    // 跳转到拼场详情
    goToSharingDetail(sharingId) {
      console.log("跳转到拼场详情，sharingId:", sharingId);
      if (!sharingId) {
        console.error("sharingId为空，无法跳转");
        common_vendor.index.showToast({
          title: "订单ID不存在",
          icon: "error"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/sharing/detail?id=${sharingId}`
      });
    },
    // 跳转到拼场列表
    goToSharingList() {
      common_vendor.index.navigateTo({
        url: "/pages/sharing/list"
      });
    },
    // 获取进度百分比
    getProgressPercent(current, max) {
      if (!max || max === 0)
        return 0;
      return Math.round(current / max * 100);
    },
    // 格式化活动时间
    formatActivityTime(request) {
      if (!request)
        return "--";
      if (request.bookingTime) {
        try {
          let bookingTimeStr = request.bookingTime;
          if (typeof bookingTimeStr === "string" && bookingTimeStr.includes(" ") && !bookingTimeStr.includes("T")) {
            bookingTimeStr = bookingTimeStr.replace(" ", "T");
          }
          const bookingTime = new Date(bookingTimeStr);
          console.log("拼场申请时间转换 - bookingTime:", request.bookingTime, "→", bookingTimeStr, "→", bookingTime);
          console.log("拼场申请时间字段 - startTime:", request.startTime, "endTime:", request.endTime);
          const dateStr = bookingTime.toLocaleDateString("zh-CN", {
            month: "2-digit",
            day: "2-digit"
          });
          const startTimeStr = bookingTime.toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false
          });
          let timeSlot2 = startTimeStr;
          if (request.startTime && request.endTime) {
            timeSlot2 = `${request.startTime}-${request.endTime}`;
          } else if (request.endTime) {
            timeSlot2 = `${startTimeStr}-${request.endTime}`;
          }
          return `${dateStr} ${timeSlot2}`;
        } catch (error) {
          console.error("时间格式化错误:", error);
          return "--";
        }
      }
      const date = this.formatDate(request.bookingDate);
      const timeSlot = this.formatTimeSlot(request.startTime, request.endTime);
      return `${date} ${timeSlot}`;
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "--";
      return utils_helpers.formatDate(date, "MM-DD");
    },
    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime)
        return "--";
      return utils_helpers.formatDateTime(datetime, "MM-DD HH:mm");
    },
    // 格式化时间段
    formatTimeSlot(startTime, endTime) {
      if (!startTime && !endTime) {
        return "时间未指定";
      }
      if (startTime && !endTime) {
        return startTime;
      }
      if (!startTime && endTime) {
        return endTime;
      }
      return `${startTime}-${endTime}`;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "APPROVED_PENDING_PAYMENT": "已批准待支付",
        "APPROVED": "已完成",
        "PAID": "拼场成功",
        "REJECTED": "已拒绝",
        "TIMEOUT_CANCELLED": "超时取消"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        "PENDING": "status-pending",
        "APPROVED_PENDING_PAYMENT": "status-pending",
        "APPROVED": "status-approved",
        "PAID": "status-success",
        "REJECTED": "status-rejected",
        "TIMEOUT_CANCELLED": "status-cancelled"
      };
      return classMap[status] || "status-unknown";
    },
    // 获取空状态标题
    getEmptyTitle() {
      const titleMap = {
        "all": "暂无申请记录",
        "pending": "暂无待处理申请",
        "approved": "暂无已通过申请",
        "rejected": "暂无被拒绝申请"
      };
      return titleMap[this.currentFilter] || "暂无申请记录";
    },
    // 获取空状态描述
    getEmptyDesc() {
      const descMap = {
        "all": "快去申请加入感兴趣的拼场吧",
        "pending": "您的申请都已被处理",
        "approved": "暂时没有通过的申请",
        "rejected": "暂时没有被拒绝的申请"
      };
      return descMap[this.currentFilter] || "快去申请加入感兴趣的拼场吧";
    },
    // 获取申请价格
    getRequestPrice(request) {
      if (!request)
        return "0.00";
      const price = request.paymentAmount || request.pricePerPerson || request.totalPrice || 0;
      return typeof price === "number" ? price.toFixed(2) : "0.00";
    }
  }
};
if (!Array) {
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  (_component_uni_popup_dialog + _component_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.filterTabs, (tab, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.label),
        b: tab.count > 0
      }, tab.count > 0 ? {
        c: common_vendor.t(tab.count)
      } : {}, {
        d: tab.value,
        e: $data.currentFilter === tab.value ? 1 : "",
        f: common_vendor.o(($event) => $options.switchFilter(tab.value), tab.value)
      });
    }),
    c: $options.loading
  }, $options.loading ? {} : $data.error ? {
    e: common_vendor.t($data.error),
    f: common_vendor.o((...args) => $options.loadRequests && $options.loadRequests(...args))
  } : common_vendor.e({
    g: $options.filteredRequests.length > 0
  }, $options.filteredRequests.length > 0 ? {
    h: common_vendor.f($options.filteredRequests, (request, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(request.venueName),
        b: common_vendor.t($options.getStatusText(request.status)),
        c: common_vendor.n($options.getStatusClass(request.status)),
        d: common_vendor.t(request.teamName || request.applicantTeamName),
        e: common_vendor.t($options.formatActivityTime(request)),
        f: common_vendor.t($options.getRequestPrice(request)),
        g: common_vendor.t(request.currentParticipants),
        h: common_vendor.t(request.maxParticipants),
        i: common_vendor.t($options.getProgressPercent(request.currentParticipants, request.maxParticipants)),
        j: $options.getProgressPercent(request.currentParticipants, request.maxParticipants) + "%",
        k: common_vendor.t($options.formatDateTime(request.createdAt)),
        l: request.processedAt
      }, request.processedAt ? {
        m: common_vendor.t($options.formatDateTime(request.processedAt))
      } : {}, {
        n: request.status === "PENDING"
      }, request.status === "PENDING" ? {
        o: common_vendor.o(($event) => $options.showCancelConfirm(request), request.id)
      } : request.status === "APPROVED" ? {
        q: common_vendor.o(($event) => $options.goToSharingDetail(request.orderId || request.sharingId), request.id)
      } : request.status === "REJECTED" ? common_vendor.e({
        s: request.rejectReason
      }, request.rejectReason ? {
        t: common_vendor.t(request.rejectReason)
      } : {}) : {}, {
        p: request.status === "APPROVED",
        r: request.status === "REJECTED",
        v: request.id,
        w: common_vendor.o(($event) => $options.goToSharingDetail(request.orderId || request.sharingId), request.id)
      });
    })
  } : {
    i: common_vendor.t($options.getEmptyTitle()),
    j: common_vendor.t($options.getEmptyDesc()),
    k: common_vendor.o((...args) => $options.goToSharingList && $options.goToSharingList(...args))
  }), {
    d: $data.error,
    l: common_vendor.o($options.confirmCancel),
    m: common_vendor.o($options.closeCancelPopup),
    n: common_vendor.p({
      type: "warn",
      title: "取消申请",
      content: `确定要取消对 ${(_a = $data.cancelTarget) == null ? void 0 : _a.teamName} 的申请吗？`
    }),
    o: common_vendor.sr("cancelPopup", "3975ffc5-0"),
    p: $data.internalCancelPopupOpened,
    q: common_vendor.n($data.cancelPopupPosition),
    r: common_vendor.p({
      type: "dialog",
      ["mask-click"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3975ffc5"]]);
wx.createPage(MiniProgramPage);
