
.container.data-v-d1a1fc53 {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-d1a1fc53 {
  text-align: center;
  margin-bottom: 60rpx;
}
.title.data-v-d1a1fc53 {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-d1a1fc53 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.category-section.data-v-d1a1fc53 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-d1a1fc53 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.category-grid.data-v-d1a1fc53 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.category-btn.data-v-d1a1fc53 {
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-btn.a-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}
.category-btn.b-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #4ecdc4, #44a08d);
}
.category-btn.c-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #45b7d1, #96c93d);
}
.category-btn.d-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #f093fb, #f5576c);
}
.category-btn.e-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #4facfe, #00f2fe);
}
.category-btn.f-class.data-v-d1a1fc53 { background: linear-gradient(135deg, #43e97b, #38f9d7);
}
.full-check-btn.data-v-d1a1fc53 {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.progress-section.data-v-d1a1fc53 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.progress-bar.data-v-d1a1fc53 {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.progress-fill.data-v-d1a1fc53 {
  height: 100%;
  background: linear-gradient(90deg, #4facfe, #00f2fe);
  transition: width 0.3s ease;
}
.progress-text.data-v-d1a1fc53 {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  display: block;
}
.stats-section.data-v-d1a1fc53 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.stats-grid.data-v-d1a1fc53 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stat-item.data-v-d1a1fc53 {
  text-align: center;
  padding: 30rpx;
  border-radius: 15rpx;
}
.stat-item.success.data-v-d1a1fc53 { background-color: #e8f5e8;
}
.stat-item.warning.data-v-d1a1fc53 { background-color: #fff3e0;
}
.stat-item.error.data-v-d1a1fc53 { background-color: #ffebee;
}
.stat-item.info.data-v-d1a1fc53 { background-color: #e3f2fd;
}
.stat-number.data-v-d1a1fc53 {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.stat-item.success .stat-number.data-v-d1a1fc53 { color: #4caf50;
}
.stat-item.warning .stat-number.data-v-d1a1fc53 { color: #ff9800;
}
.stat-item.error .stat-number.data-v-d1a1fc53 { color: #f44336;
}
.stat-item.info .stat-number.data-v-d1a1fc53 { color: #2196f3;
}
.stat-label.data-v-d1a1fc53 {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.results-section.data-v-d1a1fc53 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.results-container.data-v-d1a1fc53 {
  max-height: 1000rpx;
  overflow-y: auto;
}
.result-item.data-v-d1a1fc53 {
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 15rpx;
  border-left: 6rpx solid;
}
.result-item.success.data-v-d1a1fc53 {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
}
.result-item.warning.data-v-d1a1fc53 {
  background-color: #fff3e0;
  border-left-color: #ff9800;
}
.result-item.error.data-v-d1a1fc53 {
  background-color: #ffebee;
  border-left-color: #f44336;
}
.result-header.data-v-d1a1fc53 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.result-title.data-v-d1a1fc53 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.result-status.data-v-d1a1fc53 {
  font-size: 24rpx;
  font-weight: bold;
}
.result-status.success.data-v-d1a1fc53 { color: #4caf50;
}
.result-status.warning.data-v-d1a1fc53 { color: #ff9800;
}
.result-status.error.data-v-d1a1fc53 { color: #f44336;
}
.result-description.data-v-d1a1fc53 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}
.result-suggestion.data-v-d1a1fc53 {
  font-size: 24rpx;
  color: #2196f3;
  line-height: 1.4;
  display: block;
  font-style: italic;
}
