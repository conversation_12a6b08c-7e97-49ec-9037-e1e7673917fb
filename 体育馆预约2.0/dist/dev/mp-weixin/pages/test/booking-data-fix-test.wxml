<view class="test-container data-v-a3608043"><view class="header data-v-a3608043"><text class="title data-v-a3608043">🔧 预约数据修复测试</text><text class="subtitle data-v-a3608043">测试包场和拼场订单数据传递及时间段状态更新</text></view><view class="control-panel data-v-a3608043"><view class="input-group data-v-a3608043"><text class="label data-v-a3608043">场馆ID:</text><input type="number" placeholder="输入场馆ID" class="input data-v-a3608043" value="{{a}}" bindinput="{{b}}"/></view><view class="input-group data-v-a3608043"><text class="label data-v-a3608043">测试日期:</text><input type="date" class="input data-v-a3608043" value="{{c}}" bindinput="{{d}}"/></view><view class="button-group data-v-a3608043"><button bindtap="{{e}}" class="test-btn primary data-v-a3608043">🏟️ 测试包场预约</button><button bindtap="{{f}}" class="test-btn secondary data-v-a3608043">🤝 测试拼场预约</button><button bindtap="{{g}}" class="test-btn success data-v-a3608043">⏰ 检查时间段状态</button><button bindtap="{{h}}" class="test-btn warning data-v-a3608043">🗑️ 清除结果</button></view></view><view class="results-section data-v-a3608043"><text class="section-title data-v-a3608043">测试结果</text><scroll-view class="results-container data-v-a3608043" scroll-y><view wx:for="{{i}}" wx:for-item="result" wx:key="h" class="result-item data-v-a3608043"><view class="result-header data-v-a3608043"><text class="result-title data-v-a3608043">{{result.a}}</text><text class="{{['data-v-a3608043', 'result-status', result.c]}}">{{result.b}}</text><text class="result-time data-v-a3608043">{{result.d}}</text></view><view class="result-details data-v-a3608043"><text class="detail-text data-v-a3608043">{{result.e}}</text><view wx:if="{{result.f}}" class="data-section data-v-a3608043"><text class="data-title data-v-a3608043">详细数据:</text><text class="data-content data-v-a3608043">{{result.g}}</text></view></view></view></scroll-view></view><view wx:if="{{j}}" class="timeslots-section data-v-a3608043"><text class="section-title data-v-a3608043">当前时间段状态 ({{k}}个)</text><view class="timeslot-grid data-v-a3608043"><view wx:for="{{l}}" wx:for-item="slot" wx:key="e" class="{{['data-v-a3608043', 'timeslot-item', slot.f]}}"><text class="slot-time data-v-a3608043">{{slot.a}}-{{slot.b}}</text><text class="slot-price data-v-a3608043">¥{{slot.c}}</text><text class="slot-status data-v-a3608043">{{slot.d}}</text></view></view></view></view>