
.test-container.data-v-4c110a64 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-4c110a64 {
  text-align: center;
  margin-bottom: 30rpx;
}
.title.data-v-4c110a64 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.subtitle.data-v-4c110a64 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.control-panel.data-v-4c110a64 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.input-group.data-v-4c110a64 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.label.data-v-4c110a64 {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
}
.input.data-v-4c110a64 {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.button-group.data-v-4c110a64 {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}
.test-btn.data-v-4c110a64 {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}
.primary.data-v-4c110a64 {
  background: #007AFF;
  color: white;
}
.secondary.data-v-4c110a64 {
  background: #34C759;
  color: white;
}
.warning.data-v-4c110a64 {
  background: #FF9500;
  color: white;
}
.results-section.data-v-4c110a64, .timeslots-section.data-v-4c110a64, .venue-info.data-v-4c110a64 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.section-title.data-v-4c110a64 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.result-item.data-v-4c110a64 {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.result-header.data-v-4c110a64 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.result-title.data-v-4c110a64 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.result-status.success.data-v-4c110a64 {
  color: #34C759;
}
.result-status.error.data-v-4c110a64 {
  color: #FF3B30;
}
.detail-text.data-v-4c110a64 {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}
.data-section.data-v-4c110a64 {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 15rpx;
}
.data-title.data-v-4c110a64 {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.data-content.data-v-4c110a64 {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
  white-space: pre-wrap;
  display: block;
}
.timeslot-grid.data-v-4c110a64 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 15rpx;
}
.timeslot-item.data-v-4c110a64 {
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 15rpx;
  text-align: center;
}
.timeslot-item.status-available.data-v-4c110a64 {
  background: #e8f5e8;
  border-color: #34C759;
}
.timeslot-item.status-reserved.data-v-4c110a64 {
  background: #ffe8e8;
  border-color: #FF3B30;
}
.slot-time.data-v-4c110a64 {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}
.slot-price.data-v-4c110a64 {
  font-size: 22rpx;
  color: #007AFF;
  display: block;
  margin-bottom: 5rpx;
}
.slot-status.data-v-4c110a64 {
  font-size: 20rpx;
  color: #666;
  display: block;
}
.info-item.data-v-4c110a64 {
  display: flex;
  margin-bottom: 15rpx;
}
.info-label.data-v-4c110a64 {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.info-value.data-v-4c110a64 {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}
