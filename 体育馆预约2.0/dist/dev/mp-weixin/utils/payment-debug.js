"use strict";
const common_vendor = require("../common/vendor.js");
function debugOrderAmount(orderData) {
  console.log("=== 订单金额调试 ===");
  console.log("订单数据:", orderData);
  const { venueId, timeSlot, duration = 1, price = 0 } = orderData || {};
  const totalAmount = price * duration;
  const debugInfo = {
    venueId,
    timeSlot,
    duration,
    unitPrice: price,
    totalAmount,
    calculation: `${price} × ${duration} = ${totalAmount}`,
    timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
  };
  console.log("计算结果:", debugInfo);
  console.log("===================");
  return debugInfo;
}
async function debugTimeSlotRefresh(venueId, date) {
  var _a;
  console.log("=== 时间段刷新调试 ===");
  console.log("场馆ID:", venueId);
  console.log("日期:", date);
  const startTime = Date.now();
  try {
    const response = await new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            { id: 1, time: "09:00-10:00", status: "available", price: 100 },
            { id: 2, time: "10:00-11:00", status: "occupied", price: 100 },
            { id: 3, time: "11:00-12:00", status: "maintenance", price: 100 }
          ]
        });
      }, 500);
    });
    const endTime = Date.now();
    const duration = endTime - startTime;
    const debugInfo = {
      venueId,
      date,
      success: true,
      responseTime: `${duration}ms`,
      dataCount: ((_a = response.data) == null ? void 0 : _a.length) || 0,
      timestamp: (/* @__PURE__ */ new Date()).toLocaleString(),
      response
    };
    console.log("刷新成功:", debugInfo);
    console.log("===================");
    return debugInfo;
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const debugInfo = {
      venueId,
      date,
      success: false,
      responseTime: `${duration}ms`,
      error: error.message,
      timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
    };
    console.error("刷新失败:", debugInfo);
    console.log("===================");
    return debugInfo;
  }
}
async function forceRefreshTimeSlots(venueId, date, clearCache = true) {
  var _a;
  console.log("=== 强制刷新时间段 ===");
  console.log("场馆ID:", venueId);
  console.log("日期:", date);
  console.log("清除缓存:", clearCache);
  const startTime = Date.now();
  try {
    if (clearCache) {
      console.log("正在清除缓存...");
      common_vendor.index.removeStorageSync(`timeslots_${venueId}_${date}`);
    }
    const response = await new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            { id: 1, time: "09:00-10:00", status: "available", price: 100 },
            { id: 2, time: "10:00-11:00", status: "available", price: 100 },
            { id: 3, time: "11:00-12:00", status: "occupied", price: 100 },
            { id: 4, time: "14:00-15:00", status: "maintenance", price: 100 }
          ],
          fromCache: false,
          refreshTime: (/* @__PURE__ */ new Date()).toISOString()
        });
      }, 800);
    });
    const endTime = Date.now();
    const duration = endTime - startTime;
    const debugInfo = {
      venueId,
      date,
      clearCache,
      success: true,
      responseTime: `${duration}ms`,
      dataCount: ((_a = response.data) == null ? void 0 : _a.length) || 0,
      fromCache: response.fromCache,
      refreshTime: response.refreshTime,
      timestamp: (/* @__PURE__ */ new Date()).toLocaleString(),
      response
    };
    console.log("强制刷新成功:", debugInfo);
    console.log("===================");
    return debugInfo;
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const debugInfo = {
      venueId,
      date,
      clearCache,
      success: false,
      responseTime: `${duration}ms`,
      error: error.message,
      timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
    };
    console.error("强制刷新失败:", debugInfo);
    console.log("===================");
    return debugInfo;
  }
}
exports.debugOrderAmount = debugOrderAmount;
exports.debugTimeSlotRefresh = debugTimeSlotRefresh;
exports.forceRefreshTimeSlots = forceRefreshTimeSlots;
