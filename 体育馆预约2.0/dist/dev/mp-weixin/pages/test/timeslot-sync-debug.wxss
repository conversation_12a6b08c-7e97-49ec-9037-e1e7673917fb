
.debug-container.data-v-3ac421fa {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-3ac421fa {
  text-align: center;
  margin-bottom: 30rpx;
}
.title.data-v-3ac421fa {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.subtitle.data-v-3ac421fa {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.control-panel.data-v-3ac421fa, .logs-section.data-v-3ac421fa, .status-section.data-v-3ac421fa {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.input-group.data-v-3ac421fa {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.label.data-v-3ac421fa {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
}
.input.data-v-3ac421fa {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.button-group.data-v-3ac421fa {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-top: 30rpx;
}
.btn.data-v-3ac421fa {
  flex: 1;
  min-width: 200rpx;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  color: white;
}
.primary.data-v-3ac421fa { background: #007AFF;
}
.secondary.data-v-3ac421fa { background: #5856D6;
}
.success.data-v-3ac421fa { background: #34C759;
}
.warning.data-v-3ac421fa { background: #FF9500;
}
.section-title.data-v-3ac421fa {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.logs-container.data-v-3ac421fa {
  height: 600rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 15rpx;
}
.log-item.data-v-3ac421fa {
  border-radius: 8rpx;
  padding: 15rpx;
  margin-bottom: 10rpx;
}
.log-item.info.data-v-3ac421fa { background: #e3f2fd;
}
.log-item.success.data-v-3ac421fa { background: #e8f5e8;
}
.log-item.warning.data-v-3ac421fa { background: #fff3e0;
}
.log-item.error.data-v-3ac421fa { background: #ffebee;
}
.log-time.data-v-3ac421fa {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}
.log-message.data-v-3ac421fa {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.log-data.data-v-3ac421fa {
  background: #f5f5f5;
  border-radius: 6rpx;
  padding: 10rpx;
}
.data-content.data-v-3ac421fa {
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
  white-space: pre-wrap;
  display: block;
}
.status-item.data-v-3ac421fa {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}
.status-label.data-v-3ac421fa {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.status-value.data-v-3ac421fa {
  font-size: 28rpx;
  color: #007AFF;
}
