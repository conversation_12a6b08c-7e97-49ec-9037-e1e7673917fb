/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.debug-container.data-v-8a027c19 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-8a027c19 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.header .title.data-v-8a027c19 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.header .refresh-btn.data-v-8a027c19 {
  padding: 10rpx 20rpx;
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.section.data-v-8a027c19 {
  background-color: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
}
.section .section-title.data-v-8a027c19 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.param-item.data-v-8a027c19 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.param-item .param-label.data-v-8a027c19 {
  width: 150rpx;
  font-size: 28rpx;
  color: #666;
}
.param-item .param-input.data-v-8a027c19 {
  flex: 1;
  padding: 15rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.test-btn.data-v-8a027c19 {
  width: 100%;
  padding: 20rpx;
  background-color: #ff6b35;
  color: white;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
}
.response-info.data-v-8a027c19 {
  margin-bottom: 20rpx;
}
.response-info .info-item.data-v-8a027c19 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.response-data .data-title.data-v-8a027c19 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.response-data .data-content.data-v-8a027c19 {
  display: block;
  font-size: 24rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
}
.timeslot-list .timeslot-item.data-v-8a027c19 {
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 8rpx;
  border: 2rpx solid #ddd;
}
.timeslot-list .timeslot-item.status-available.data-v-8a027c19 {
  background-color: #e8f5e8;
  border-color: #4caf50;
}
.timeslot-list .timeslot-item.status-occupied.data-v-8a027c19, .timeslot-list .timeslot-item.status-reserved.data-v-8a027c19 {
  background-color: #ffeaea;
  border-color: #f44336;
}
.timeslot-list .timeslot-item.status-maintenance.data-v-8a027c19 {
  background-color: #fff3e0;
  border-color: #ff9800;
}
.timeslot-list .timeslot-item.status-expired.data-v-8a027c19 {
  background-color: #f5f5f5;
  border-color: #999;
}
.timeslot-list .timeslot-item .slot-info .slot-time.data-v-8a027c19 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.timeslot-list .timeslot-item .slot-info .slot-status.data-v-8a027c19,
.timeslot-list .timeslot-item .slot-info .slot-price.data-v-8a027c19,
.timeslot-list .timeslot-item .slot-info .slot-id.data-v-8a027c19 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}
.error-content.data-v-8a027c19 {
  color: #f44336;
  font-size: 28rpx;
  background-color: #ffeaea;
  padding: 20rpx;
  border-radius: 8rpx;
}
.stats-grid.data-v-8a027c19 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.stats-grid .stat-item.data-v-8a027c19 {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
.stats-grid .stat-item .stat-label.data-v-8a027c19 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.stats-grid .stat-item .stat-value.data-v-8a027c19 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}