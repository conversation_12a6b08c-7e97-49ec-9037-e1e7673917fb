
.container.data-v-ce5d596e {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-ce5d596e {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-ce5d596e {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.subtitle.data-v-ce5d596e {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.content.data-v-ce5d596e {
  height: calc(100vh - 200rpx);
}
.test-section.data-v-ce5d596e {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.section-title.data-v-ce5d596e {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 30rpx;
}
.test-item.data-v-ce5d596e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.test-label.data-v-ce5d596e {
  font-size: 28rpx;
  color: #666;
}
.test-value.data-v-ce5d596e {
  font-size: 28rpx;
  font-weight: bold;
}
.test-value.success.data-v-ce5d596e {
  color: #52c41a;
}
.test-value.error.data-v-ce5d596e {
  color: #ff4d4f;
}
.test-value.warning.data-v-ce5d596e {
  color: #faad14;
}
.test-btn.data-v-ce5d596e {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
  border: none;
  background: #1890ff;
  color: white;
}
.test-btn.primary.data-v-ce5d596e {
  background: #52c41a;
}
.test-btn.secondary.data-v-ce5d596e {
  background: #ff4d4f;
}
.test-results.data-v-ce5d596e {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.result-item.data-v-ce5d596e {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  background: #f9f9f9;
}
.result-status.data-v-ce5d596e {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.result-text.data-v-ce5d596e {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
