<view class="container data-v-2d2e88ca"><view class="header data-v-2d2e88ca"><text class="title data-v-2d2e88ca">时间段同步修复测试</text></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试参数</view><view class="test-item data-v-2d2e88ca"><text class="test-label data-v-2d2e88ca">场馆ID:</text><input placeholder="输入场馆ID" class="test-input data-v-2d2e88ca" value="{{a}}" bindinput="{{b}}"/></view><view class="test-item data-v-2d2e88ca"><text class="test-label data-v-2d2e88ca">测试日期:</text><input placeholder="YYYY-MM-DD" class="test-input data-v-2d2e88ca" value="{{c}}" bindinput="{{d}}"/></view></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试1: 检查同步状态</view><button bindtap="{{e}}" class="test-button data-v-2d2e88ca">检查同步状态</button><view wx:if="{{f}}" class="test-result data-v-2d2e88ca"><text class="result-title data-v-2d2e88ca">同步状态结果:</text><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">前端时间段: {{g}}个</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">后端时间段: {{h}}个</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">已同步: {{i}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">需要同步: {{j}}</text></view><view wx:if="{{k}}" class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">问题: {{l}}</text></view></view></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试2: 修复时间段生成</view><button bindtap="{{m}}" class="test-button data-v-2d2e88ca">修复时间段生成</button><view wx:if="{{n}}" class="test-result data-v-2d2e88ca"><text class="result-title data-v-2d2e88ca">修复生成结果:</text><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">成功: {{o}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">生成时间段: {{p}}个</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">同步到后端: {{q}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">执行步骤: {{r}}</text></view></view></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试3: 强制重新生成</view><button bindtap="{{s}}" class="test-button data-v-2d2e88ca">强制重新生成</button><view wx:if="{{t}}" class="test-result data-v-2d2e88ca"><text class="result-title data-v-2d2e88ca">强制重新生成结果:</text><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">成功: {{v}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">生成方式: {{w}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">时间段数量: {{x}}个</text></view><view wx:if="{{y}}" class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">错误: {{z}}</text></view></view></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试4: 自动修复</view><button bindtap="{{A}}" class="test-button data-v-2d2e88ca">自动修复问题</button><view wx:if="{{B}}" class="test-result data-v-2d2e88ca"><text class="result-title data-v-2d2e88ca">自动修复结果:</text><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">成功: {{C}}</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">最终时间段: {{D}}个</text></view><view class="result-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">执行步骤: {{E}}</text></view></view></view><view class="test-section data-v-2d2e88ca"><view class="section-title data-v-2d2e88ca">测试日志</view><button bindtap="{{F}}" class="test-button secondary data-v-2d2e88ca">清除日志</button><view class="test-logs data-v-2d2e88ca"><view wx:for="{{G}}" wx:for-item="log" wx:key="b" class="log-item data-v-2d2e88ca"><text class="data-v-2d2e88ca">{{log.a}}</text></view></view></view></view>