<view class="test-container data-v-f9c2d39b"><view class="test-header data-v-f9c2d39b"><text class="test-title data-v-f9c2d39b">🧪 Pinia迁移全面验证</text><text class="test-subtitle data-v-f9c2d39b">测试所有核心功能确保迁移成功</text></view><view class="status-section data-v-f9c2d39b"><text class="section-title data-v-f9c2d39b">📊 总体测试状态</text><view class="status-grid data-v-f9c2d39b"><view class="{{['status-item', 'data-v-f9c2d39b', b && 'success', c && 'error']}}"><text class="status-text data-v-f9c2d39b">{{a}}</text></view><view class="status-item data-v-f9c2d39b"><text class="status-text data-v-f9c2d39b">通过: {{d}}/{{e}}</text></view></view></view><view class="test-section data-v-f9c2d39b"><text class="section-title data-v-f9c2d39b">🔗 Store连接测试</text><view class="test-grid data-v-f9c2d39b"><view class="{{['test-item', 'data-v-f9c2d39b', g]}}"><text class="test-name data-v-f9c2d39b">User Store</text><text class="test-result data-v-f9c2d39b">{{f}}</text></view><view class="{{['test-item', 'data-v-f9c2d39b', i]}}"><text class="test-name data-v-f9c2d39b">Venue Store</text><text class="test-result data-v-f9c2d39b">{{h}}</text></view><view class="{{['test-item', 'data-v-f9c2d39b', k]}}"><text class="test-name data-v-f9c2d39b">Booking Store</text><text class="test-result data-v-f9c2d39b">{{j}}</text></view><view class="{{['test-item', 'data-v-f9c2d39b', m]}}"><text class="test-name data-v-f9c2d39b">Sharing Store</text><text class="test-result data-v-f9c2d39b">{{l}}</text></view><view class="{{['test-item', 'data-v-f9c2d39b', o]}}"><text class="test-name data-v-f9c2d39b">App Store</text><text class="test-result data-v-f9c2d39b">{{n}}</text></view></view></view><view class="test-section data-v-f9c2d39b"><text class="section-title data-v-f9c2d39b">⚡ 核心功能测试</text><view class="function-tests data-v-f9c2d39b"><view class="function-group data-v-f9c2d39b"><text class="group-title data-v-f9c2d39b">用户功能</text><button class="test-btn data-v-f9c2d39b" bindtap="{{q}}" disabled="{{r}}">{{p}}</button><text class="test-status data-v-f9c2d39b">{{s}}</text></view><view class="function-group data-v-f9c2d39b"><text class="group-title data-v-f9c2d39b">场馆功能</text><button class="test-btn data-v-f9c2d39b" bindtap="{{v}}" disabled="{{w}}">{{t}}</button><text class="test-status data-v-f9c2d39b">{{x}}</text></view><view class="function-group data-v-f9c2d39b"><text class="group-title data-v-f9c2d39b">预订功能</text><button class="test-btn data-v-f9c2d39b" bindtap="{{z}}" disabled="{{A}}">{{y}}</button><text class="test-status data-v-f9c2d39b">{{B}}</text></view><view class="function-group data-v-f9c2d39b"><text class="group-title data-v-f9c2d39b">拼场功能</text><button class="test-btn data-v-f9c2d39b" bindtap="{{D}}" disabled="{{E}}">{{C}}</button><text class="test-status data-v-f9c2d39b">{{F}}</text></view></view></view><view class="test-section data-v-f9c2d39b"><text class="section-title data-v-f9c2d39b">📱 页面导航测试</text><view class="nav-tests data-v-f9c2d39b"><button class="nav-btn data-v-f9c2d39b" bindtap="{{G}}">登录页面</button><button class="nav-btn data-v-f9c2d39b" bindtap="{{H}}">个人中心</button><button class="nav-btn data-v-f9c2d39b" bindtap="{{I}}">场馆列表</button><button class="nav-btn data-v-f9c2d39b" bindtap="{{J}}">我的预订</button><button class="nav-btn data-v-f9c2d39b" bindtap="{{K}}">拼场列表</button></view></view><view class="action-section data-v-f9c2d39b"><button class="full-test-btn data-v-f9c2d39b" bindtap="{{M}}" disabled="{{N}}" style="background-color:#007bff;margin-bottom:20rpx">{{L}}</button><button class="full-test-btn data-v-f9c2d39b" bindtap="{{P}}" disabled="{{Q}}" style="background-color:#ff6b00;margin-bottom:20rpx">{{O}}</button><button class="full-test-btn data-v-f9c2d39b" bindtap="{{S}}" disabled="{{T}}" style="background-color:#28a745;margin-bottom:20rpx">{{R}}</button><button class="full-test-btn data-v-f9c2d39b" bindtap="{{V}}" disabled="{{W}}">{{U}}</button></view><view wx:if="{{X}}" class="log-section data-v-f9c2d39b"><text class="section-title data-v-f9c2d39b">📝 测试日志</text><view class="log-container data-v-f9c2d39b"><text wx:for="{{Y}}" wx:for-item="log" wx:key="c" class="{{['log-item', 'data-v-f9c2d39b', log.d]}}">{{log.a}} - {{log.b}}</text></view></view></view>