<view class="container data-v-18d18da6"><view class="header data-v-18d18da6"><text class="title data-v-18d18da6">🔍 API直接测试</text><text class="subtitle data-v-18d18da6">测试时间段API的实际响应数据</text></view><view class="test-section data-v-18d18da6"><view class="input-group data-v-18d18da6"><text class="label data-v-18d18da6">场馆ID:</text><input type="number" placeholder="输入场馆ID" class="input data-v-18d18da6" value="{{a}}" bindinput="{{b}}"/></view><view class="input-group data-v-18d18da6"><text class="label data-v-18d18da6">日期:</text><input type="text" placeholder="YYYY-MM-DD" class="input data-v-18d18da6" value="{{c}}" bindinput="{{d}}"/></view><button bindtap="{{f}}" class="test-btn data-v-18d18da6" disabled="{{g}}">{{e}}</button></view><view wx:if="{{h}}" class="results-section data-v-18d18da6"><view class="section-title data-v-18d18da6">📋 API原始响应</view><view class="code-block data-v-18d18da6">{{i}}</view></view><view wx:if="{{j}}" class="results-section data-v-18d18da6"><view class="section-title data-v-18d18da6">📊 处理后的时间段数据</view><view class="timeslot-list data-v-18d18da6"><view wx:for="{{k}}" wx:for-item="slot" wx:key="f" class="timeslot-item data-v-18d18da6"><view class="time-range data-v-18d18da6">{{slot.a}} - {{slot.b}}</view><view class="{{['status', 'data-v-18d18da6', slot.d]}}">{{slot.c}}</view><view class="price data-v-18d18da6">¥{{slot.e}}</view></view></view></view><view wx:if="{{l}}" class="results-section data-v-18d18da6"><view class="section-title data-v-18d18da6">📈 状态统计</view><view class="stats-grid data-v-18d18da6"><view class="stat-item data-v-18d18da6"><text class="stat-label data-v-18d18da6">总数</text><text class="stat-value data-v-18d18da6">{{m}}</text></view><view class="stat-item data-v-18d18da6"><text class="stat-label data-v-18d18da6">可预约</text><text class="stat-value available data-v-18d18da6">{{n}}</text></view><view class="stat-item data-v-18d18da6"><text class="stat-label data-v-18d18da6">已预约</text><text class="stat-value reserved data-v-18d18da6">{{o}}</text></view><view class="stat-item data-v-18d18da6"><text class="stat-label data-v-18d18da6">已占用</text><text class="stat-value occupied data-v-18d18da6">{{p}}</text></view><view class="stat-item data-v-18d18da6"><text class="stat-label data-v-18d18da6">维护中</text><text class="stat-value maintenance data-v-18d18da6">{{q}}</text></view></view></view><view wx:if="{{r}}" class="results-section data-v-18d18da6"><view class="section-title data-v-18d18da6">❌ 错误信息</view><view class="error-message data-v-18d18da6">{{s}}</view></view></view>