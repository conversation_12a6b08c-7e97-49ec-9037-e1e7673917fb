<view class="container data-v-da6f6252"><view class="navbar data-v-da6f6252"><view class="nav-left data-v-da6f6252" bindtap="{{a}}"><text class="nav-icon data-v-da6f6252">‹</text></view><text class="nav-title data-v-da6f6252">创建拼场</text><view class="nav-right data-v-da6f6252"></view></view><view class="form-container data-v-da6f6252"><view class="form-section data-v-da6f6252"><view class="section-title data-v-da6f6252">选择预约</view><view class="booking-selector data-v-da6f6252" bindtap="{{g}}"><view wx:if="{{b}}" class="booking-info data-v-da6f6252"><view class="booking-venue data-v-da6f6252"><text class="venue-name data-v-da6f6252">{{c}}</text><text class="booking-status data-v-da6f6252">{{d}}</text></view><view class="booking-time data-v-da6f6252"><text class="time-text data-v-da6f6252">{{e}}</text></view><view class="booking-price data-v-da6f6252"><text class="price-text data-v-da6f6252">总费用：¥{{f}}</text></view></view><view wx:else class="booking-placeholder data-v-da6f6252"><text class="placeholder-icon data-v-da6f6252">+</text><text class="placeholder-text data-v-da6f6252">点击选择已确认的预约</text></view><text class="selector-arrow data-v-da6f6252">&gt;</text></view></view><view class="form-section data-v-da6f6252"><view class="section-title data-v-da6f6252">队伍信息</view><view class="form-card data-v-da6f6252"><view class="form-item data-v-da6f6252"><text class="form-label data-v-da6f6252">队伍名称</text><input class="form-input data-v-da6f6252" placeholder="请输入队伍名称" maxlength="20" value="{{h}}" bindinput="{{i}}"/></view><view class="form-item data-v-da6f6252"><text class="form-label data-v-da6f6252">拼场模式</text><view class="mode-display data-v-da6f6252"><text class="mode-text data-v-da6f6252">两支球队对战</text><text class="mode-desc data-v-da6f6252">固定2支球队参与</text></view></view><view class="form-item data-v-da6f6252"><text class="form-label data-v-da6f6252">每队费用</text><view class="price-input-wrapper data-v-da6f6252"><text class="price-symbol data-v-da6f6252">¥</text><input class="price-input data-v-da6f6252" type="digit" placeholder="0" value="{{j}}" bindinput="{{k}}"/></view></view><view class="form-item description-item data-v-da6f6252"><text class="form-label data-v-da6f6252">活动描述</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-da6f6252" placeholder="请描述活动内容、要求等（选填）" maxlength="200" value="{{l}}" bindinput="{{m}}"/></block><view class="char-count data-v-da6f6252"><text class="count-text data-v-da6f6252">{{n}}/200</text></view></view></view></view><view class="form-section data-v-da6f6252"><view class="section-title data-v-da6f6252">联系方式</view><view class="form-card data-v-da6f6252"><view class="form-item data-v-da6f6252"><text class="form-label data-v-da6f6252">联系电话</text><input class="form-input data-v-da6f6252" type="number" placeholder="请输入联系电话" maxlength="11" value="{{o}}" bindinput="{{p}}"/></view><view class="form-item data-v-da6f6252"><text class="form-label data-v-da6f6252">微信号</text><input class="form-input data-v-da6f6252" placeholder="请输入微信号（选填）" maxlength="30" value="{{q}}" bindinput="{{r}}"/></view></view></view><view class="form-section data-v-da6f6252"><view class="section-title data-v-da6f6252">拼场设置</view><view class="form-card data-v-da6f6252"><view class="form-item switch-item data-v-da6f6252"><view class="switch-info data-v-da6f6252"><text class="switch-label data-v-da6f6252">自动通过申请</text><text class="switch-desc data-v-da6f6252">开启后，其他用户申请加入时将自动通过</text></view><switch class="data-v-da6f6252" checked="{{s}}" bindchange="{{t}}" color="#ff6b35"/></view><view class="form-item switch-item data-v-da6f6252"><view class="switch-info data-v-da6f6252"><text class="switch-label data-v-da6f6252">允许中途退出</text><text class="switch-desc data-v-da6f6252">开启后，参与者可以在活动开始前退出</text></view><switch class="data-v-da6f6252" checked="{{v}}" bindchange="{{w}}" color="#ff6b35"/></view></view></view></view><view class="bottom-actions data-v-da6f6252"><view class="price-summary data-v-da6f6252"><text class="summary-label data-v-da6f6252">预计总费用</text><text class="summary-price data-v-da6f6252">¥{{x}}</text></view><button class="{{['create-btn', 'data-v-da6f6252', y && 'disabled']}}" bindtap="{{z}}"> 创建拼场 </button></view><uni-popup wx:if="{{I}}" class="r data-v-da6f6252" u-s="{{['d']}}" u-r="bookingPopup" u-i="da6f6252-0" bind:__l="__l" u-p="{{I}}"><view class="booking-modal data-v-da6f6252"><view class="modal-header data-v-da6f6252"><text class="modal-title data-v-da6f6252">选择预约</text><text class="modal-close data-v-da6f6252" bindtap="{{A}}">×</text></view><view class="booking-list data-v-da6f6252"><view wx:if="{{B}}" class="loading-state data-v-da6f6252"><text class="data-v-da6f6252">加载中...</text></view><view wx:elif="{{C}}" class="empty-state data-v-da6f6252"><text class="empty-icon data-v-da6f6252">📅</text><text class="empty-text data-v-da6f6252">暂无可用的预约</text><text class="empty-desc data-v-da6f6252">请先预约场馆并确认后再创建拼场</text></view><view wx:else class="data-v-da6f6252"><view wx:for="{{D}}" wx:for-item="booking" wx:key="f" class="{{['booking-item', 'data-v-da6f6252', booking.g && 'selected']}}" bindtap="{{booking.h}}"><view class="booking-content data-v-da6f6252"><view class="booking-header data-v-da6f6252"><text class="venue-name data-v-da6f6252">{{booking.a}}</text><text class="booking-status data-v-da6f6252">{{booking.b}}</text></view><view class="booking-details data-v-da6f6252"><text class="time-info data-v-da6f6252">{{booking.c}}</text><text class="price-info data-v-da6f6252">¥{{booking.d}}</text></view></view><view wx:if="{{booking.e}}" class="selected-icon data-v-da6f6252">✓</view></view></view></view><view class="modal-actions data-v-da6f6252"><button class="modal-btn cancel-btn data-v-da6f6252" bindtap="{{E}}"> 取消 </button><button class="{{['modal-btn', 'confirm-btn', 'data-v-da6f6252', F && 'disabled']}}" bindtap="{{G}}"> 确定 </button></view></view></uni-popup></view>