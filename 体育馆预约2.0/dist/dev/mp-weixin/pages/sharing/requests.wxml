<view class="container data-v-3975ffc5"><view class="navbar data-v-3975ffc5"><view class="nav-left data-v-3975ffc5" bindtap="{{a}}"><text class="nav-icon data-v-3975ffc5">‹</text></view><text class="nav-title data-v-3975ffc5">我的申请</text><view class="nav-right data-v-3975ffc5"></view></view><view class="filter-tabs data-v-3975ffc5"><view wx:for="{{b}}" wx:for-item="tab" wx:key="d" class="{{['filter-tab', 'data-v-3975ffc5', tab.e && 'active']}}" bindtap="{{tab.f}}"><text class="tab-text data-v-3975ffc5">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-count data-v-3975ffc5">{{tab.c}}</text></view></view><view wx:if="{{c}}" class="loading-state data-v-3975ffc5"><text class="data-v-3975ffc5">加载中...</text></view><view wx:elif="{{d}}" class="error-state data-v-3975ffc5"><text class="error-icon data-v-3975ffc5">⚠️</text><text class="error-text data-v-3975ffc5">{{e}}</text><button class="retry-btn data-v-3975ffc5" bindtap="{{f}}"> 重新加载 </button></view><view wx:else class="content data-v-3975ffc5"><view wx:if="{{g}}" class="requests-list data-v-3975ffc5"><view wx:for="{{h}}" wx:for-item="request" wx:key="v" class="request-item data-v-3975ffc5" bindtap="{{request.w}}"><view class="sharing-info data-v-3975ffc5"><view class="sharing-header data-v-3975ffc5"><text class="venue-name data-v-3975ffc5">{{request.a}}</text><view class="{{['status-badge', 'data-v-3975ffc5', request.c]}}"><text class="status-text data-v-3975ffc5">{{request.b}}</text></view></view><view class="sharing-details data-v-3975ffc5"><text class="team-name data-v-3975ffc5">{{request.d}}</text><text class="activity-time data-v-3975ffc5">{{request.e}}</text><text class="price data-v-3975ffc5">支付金额 ¥{{request.f}}</text></view><view class="participants-progress data-v-3975ffc5"><view class="progress-info data-v-3975ffc5"><text class="progress-text data-v-3975ffc5">{{request.g}}/{{request.h}}人 </text><text class="progress-percent data-v-3975ffc5">{{request.i}}% </text></view><view class="progress-bar data-v-3975ffc5"><view class="progress-fill data-v-3975ffc5" style="{{'width:' + request.j}}"></view></view></view></view><view class="request-info data-v-3975ffc5"><view class="request-meta data-v-3975ffc5"><text class="request-time data-v-3975ffc5">申请时间：{{request.k}}</text><text wx:if="{{request.l}}" class="process-time data-v-3975ffc5"> 处理时间：{{request.m}}</text></view><view class="request-actions data-v-3975ffc5"><view wx:if="{{request.n}}" class="pending-actions data-v-3975ffc5"><button class="action-btn cancel-btn data-v-3975ffc5" catchtap="{{request.o}}"> 取消申请 </button></view><view wx:elif="{{request.p}}" class="approved-actions data-v-3975ffc5"><text class="approved-text data-v-3975ffc5">申请已通过</text><button class="action-btn join-btn data-v-3975ffc5" catchtap="{{request.q}}"> 查看详情 </button></view><view wx:elif="{{request.r}}" class="rejected-actions data-v-3975ffc5"><text class="rejected-text data-v-3975ffc5">申请被拒绝</text><text wx:if="{{request.s}}" class="reject-reason data-v-3975ffc5"> 原因：{{request.t}}</text></view></view></view></view></view><view wx:else class="empty-state data-v-3975ffc5"><text class="empty-icon data-v-3975ffc5">📝</text><text class="empty-title data-v-3975ffc5">{{i}}</text><text class="empty-desc data-v-3975ffc5">{{j}}</text><button class="browse-btn data-v-3975ffc5" bindtap="{{k}}"> 去看看拼场 </button></view></view><uni-popup wx:if="{{r}}" u-s="{{['d']}}" u-r="cancelPopup" data-c-h="{{!p}}" class="{{['r', 'data-v-3975ffc5', q]}}" u-i="3975ffc5-0" bind:__l="__l" u-p="{{r}}"><uni-popup-dialog wx:if="{{n}}" class="data-v-3975ffc5" bindconfirm="{{l}}" bindclose="{{m}}" u-i="3975ffc5-1,3975ffc5-0" bind:__l="__l" u-p="{{n}}"></uni-popup-dialog></uni-popup></view>