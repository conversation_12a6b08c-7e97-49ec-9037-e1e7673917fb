<view class="payment-container data-v-44be683b"><view class="nav-bar data-v-44be683b"><view class="nav-left data-v-44be683b" bindtap="{{a}}"><text class="nav-icon data-v-44be683b">←</text></view><view class="nav-title data-v-44be683b">订单支付</view><view class="nav-right data-v-44be683b"></view></view><view wx:if="{{b}}" class="loading-container data-v-44be683b"><text class="loading-text data-v-44be683b">加载中...</text></view><view wx:elif="{{c}}" class="order-section data-v-44be683b"><view class="order-header data-v-44be683b"><text class="order-title data-v-44be683b">订单信息</text><text class="order-no data-v-44be683b">{{d}}</text></view><view class="order-details data-v-44be683b"><view class="detail-item data-v-44be683b"><text class="detail-label data-v-44be683b">场馆名称</text><text class="detail-value data-v-44be683b">{{e}}</text></view><view class="detail-item data-v-44be683b"><text class="detail-label data-v-44be683b">预约时间</text><text class="detail-value data-v-44be683b">{{f}}</text></view><view class="detail-item data-v-44be683b"><text class="detail-label data-v-44be683b">预约类型</text><text class="detail-value data-v-44be683b">{{g}}</text></view><view wx:if="{{h}}" class="detail-item data-v-44be683b"><text class="detail-label data-v-44be683b">队伍名称</text><text class="detail-value data-v-44be683b">{{i}}</text></view><view class="detail-item data-v-44be683b"><text class="detail-label data-v-44be683b">联系方式</text><text class="detail-value data-v-44be683b">{{j}}</text></view></view><view class="price-section data-v-44be683b"><view class="price-item data-v-44be683b"><text class="price-label data-v-44be683b">订单金额</text><text class="price-value data-v-44be683b">¥{{k}}</text></view><view wx:if="{{l}}" class="price-note data-v-44be683b"><text class="note-text data-v-44be683b">* 拼场订单按队伍收费</text></view></view></view><view class="payment-methods data-v-44be683b"><view class="method-header data-v-44be683b"><text class="method-title data-v-44be683b">支付方式</text></view><view class="method-list data-v-44be683b"><view class="{{['method-item', 'data-v-44be683b', n && 'active']}}" bindtap="{{o}}"><view class="method-info data-v-44be683b"><text class="method-icon data-v-44be683b">💳</text><text class="method-name data-v-44be683b">微信支付</text></view><view class="method-radio data-v-44be683b"><text wx:if="{{m}}" class="radio-checked data-v-44be683b">✓</text></view></view><view class="{{['method-item', 'data-v-44be683b', q && 'active']}}" bindtap="{{r}}"><view class="method-info data-v-44be683b"><text class="method-icon data-v-44be683b">💰</text><text class="method-name data-v-44be683b">支付宝</text></view><view class="method-radio data-v-44be683b"><text wx:if="{{p}}" class="radio-checked data-v-44be683b">✓</text></view></view></view></view><view class="payment-footer data-v-44be683b"><view class="footer-info data-v-44be683b"><text class="footer-label data-v-44be683b">应付金额</text><text class="footer-amount data-v-44be683b">¥{{s}}</text></view><button class="{{['pay-button', 'data-v-44be683b', v && 'disabled']}}" disabled="{{w}}" bindtap="{{x}}">{{t}}</button></view><uni-popup wx:if="{{I}}" u-s="{{['d']}}" u-r="resultPopup" data-c-h="{{!G}}" class="{{['r', 'data-v-44be683b', H]}}" u-i="44be683b-0" bind:__l="__l" u-p="{{I}}"><view class="result-popup data-v-44be683b"><view class="popup-close-icon data-v-44be683b" bindtap="{{y}}"><text class="close-x data-v-44be683b">×</text></view><view class="result-icon data-v-44be683b"><text wx:if="{{z}}" class="success-icon data-v-44be683b">✓</text><text wx:else class="error-icon data-v-44be683b">✗</text></view><text class="result-title data-v-44be683b">{{A}}</text><text class="result-message data-v-44be683b">{{B}}</text><view class="popup-actions data-v-44be683b"><button class="result-button data-v-44be683b" bindtap="{{D}}">{{C}}</button><button class="popup-close-button data-v-44be683b" bindtap="{{E}}"> 关闭 </button></view></view></uni-popup></view>