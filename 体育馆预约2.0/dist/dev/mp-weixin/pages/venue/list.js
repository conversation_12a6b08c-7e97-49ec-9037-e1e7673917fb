"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_venue = require("../../stores/venue.js");
const _sfc_main = {
  name: "VenueList",
  components: {
    // 使用easycom自动注册组件
  },
  data() {
    return {
      venueStore: null,
      searchKeyword: "",
      selectedType: "",
      searchInputTimer: null,
      // 搜索输入防抖定时器
      // 弹窗状态控制
      internalPopupOpened: false,
      popupPosition: null,
      // 存储弹窗位置信息
      _popupRef: null,
      // 缓存弹窗引用
      // 筛选选项
      filterOptions: {
        minPrice: "",
        maxPrice: "",
        distance: "",
        facilities: []
      },
      // 距离选项
      distanceOptions: [
        { label: "1km内", value: 1 },
        { label: "3km内", value: 3 },
        { label: "5km内", value: 5 },
        { label: "10km内", value: 10 }
      ],
      // 设施选项
      facilityOptions: [
        { label: "停车场", value: "parking" },
        { label: "淋浴间", value: "shower" },
        { label: "更衣室", value: "locker" },
        { label: "WiFi", value: "wifi" },
        { label: "空调", value: "ac" },
        { label: "器材租赁", value: "equipment" }
      ],
      // 缓存相关
      lastRefreshTime: 0,
      cacheTimeout: 3e4,
      // 30秒缓存
      isRefreshing: false
    };
  },
  computed: {
    venueList() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.venueListGetter) || [];
    },
    venueTypes() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.venueTypesGetter) || [];
    },
    searchResults() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.searchResultsGetter) || [];
    },
    loading() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.isLoading) || false;
    },
    pagination() {
      var _a;
      return ((_a = this.venueStore) == null ? void 0 : _a.getPagination) || { current: 0, totalPages: 0 };
    },
    filteredVenues() {
      if (this.searchKeyword && this.searchKeyword.trim()) {
        console.log("使用搜索结果:", this.searchResults);
        if (!Array.isArray(this.searchResults) || this.searchResults.length === 0) {
          console.log("后端搜索结果为空，使用前端过滤");
          const keyword = this.searchKeyword.trim().toLowerCase();
          const filtered = this.venueList.filter((venue) => {
            if (!venue)
              return false;
            const nameMatch = venue.name && venue.name.toLowerCase().includes(keyword);
            const locationMatch = venue.location && venue.location.toLowerCase().includes(keyword);
            const typeMatch = venue.type && venue.type.toLowerCase().includes(keyword);
            return nameMatch || locationMatch || typeMatch;
          });
          console.log("前端过滤结果:", filtered.length, "个场馆");
          return filtered;
        }
        return Array.isArray(this.searchResults) ? this.searchResults : [];
      }
      console.log("使用场馆列表:", this.venueList);
      return Array.isArray(this.venueList) ? this.venueList : [];
    },
    hasMore() {
      return this.pagination && this.pagination.current < this.pagination.totalPages;
    }
  },
  onLoad() {
    console.log("VenueList组件已加载");
    this.venueStore = stores_venue.useVenueStore();
    if (!this.venueStore) {
      console.error("VenueStore初始化失败");
      common_vendor.index.showToast({
        title: "Store初始化失败",
        icon: "error"
      });
      return;
    }
    console.log("VenueStore初始化成功:", {
      store: !!this.venueStore,
      getVenueList: typeof this.venueStore.getVenueList,
      getVenueTypes: typeof this.venueStore.getVenueTypes
    });
    this.initData();
    setTimeout(() => {
      console.log("尝试预先初始化popup组件引用");
      if (this.$refs.filterPopup) {
        console.log("onLoad中filterPopup ref已就绪");
        this._popupRef = this.$refs.filterPopup;
      }
    }, 100);
  },
  onShow() {
    this.refreshDataWithCache();
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  onUnload() {
    if (this.searchInputTimer) {
      clearTimeout(this.searchInputTimer);
      this.searchInputTimer = null;
    }
  },
  onReady() {
    console.log("VenueList组件已就绪");
    this.$nextTick(() => {
      setTimeout(() => {
        let popup = this.$refs.filterPopup;
        if (popup) {
          console.log("filterPopup ref已就绪");
          this._popupRef = popup;
        } else {
          console.log("filterPopup ref未就绪，可能需要手动导入uni-popup组件");
          setTimeout(() => {
            let popup2 = this.$refs.filterPopup;
            if (popup2) {
              console.log("filterPopup ref在第二次尝试时已就绪");
              this._popupRef = popup2;
            } else {
              setTimeout(() => {
                let popup3 = this.$refs.filterPopup;
                if (popup3) {
                  console.log("filterPopup ref在第三次尝试时已就绪");
                  this._popupRef = popup3;
                }
              }, 1e3);
            }
          }, 500);
        }
      }, 500);
    });
  },
  // 添加onMounted生命周期钩子
  onMounted() {
    console.log("VenueList组件已挂载");
    this.$nextTick(() => {
      const tryGetPopupRef = (attempt = 1) => {
        console.log(`尝试获取popup引用 (第${attempt}次)`);
        const popup = this.$refs.filterPopup;
        if (popup) {
          console.log(`popup引用在onMounted中获取成功 (第${attempt}次)`);
          this._popupRef = popup;
          return true;
        }
        return false;
      };
      if (!tryGetPopupRef(1)) {
        setTimeout(() => {
          if (!tryGetPopupRef(2)) {
            setTimeout(() => {
              if (!tryGetPopupRef(3)) {
                setTimeout(() => {
                  tryGetPopupRef(4);
                }, 1e3);
              }
            }, 500);
          }
        }, 200);
      }
    });
  },
  mounted() {
    console.log("VenueList组件已挂载");
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        console.log("开始初始化场馆数据...");
        const results = await Promise.all([
          this.venueStore.getVenueList({ page: 1, pageSize: 50 }),
          this.venueStore.getVenueTypes()
        ]);
        console.log("场馆数据获取结果:", results);
        console.log("当前场馆列表:", this.venueList);
        console.log("当前场馆类型:", this.venueTypes);
        this.updatePagination();
      } catch (error) {
        console.error("初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "数据加载失败",
          icon: "none"
        });
      }
    },
    // 刷新数据
    async refreshData() {
      try {
        await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true });
        this.updatePagination();
        common_vendor.index.stopPullDownRefresh();
      } catch (error) {
        common_vendor.index.stopPullDownRefresh();
        console.error("刷新数据失败:", error);
      }
    },
    // 加载更多
    async loadMore() {
      if (this.searchKeyword && this.searchKeyword.trim()) {
        console.log("搜索状态下不允许加载更多");
        return;
      }
      if (this.loading || !this.hasMore)
        return;
      try {
        const nextPage = this.pagination.current + 1;
        const params = {
          page: nextPage,
          pageSize: 50
        };
        if (this.selectedType) {
          params.type = this.selectedType;
        }
        if (this.filterOptions.minPrice) {
          params.minPrice = Number(this.filterOptions.minPrice);
        }
        if (this.filterOptions.maxPrice) {
          params.maxPrice = Number(this.filterOptions.maxPrice);
        }
        if (this.filterOptions.distance) {
          params.distance = this.filterOptions.distance;
        }
        if (this.filterOptions.facilities && Array.isArray(this.filterOptions.facilities) && this.filterOptions.facilities.length > 0) {
          params.facilities = this.filterOptions.facilities.join(",");
        }
        await this.venueStore.getVenueList(params);
        this.updatePagination();
      } catch (error) {
        console.error("加载更多失败:", error);
        common_vendor.index.showToast({
          title: "加载更多失败",
          icon: "error"
        });
      }
    },
    // 更新分页状态
    updatePagination() {
    },
    // 搜索输入处理
    onSearchInput() {
      if (this.searchInputTimer) {
        clearTimeout(this.searchInputTimer);
      }
      this.searchInputTimer = setTimeout(() => {
        try {
          if (!this.searchKeyword || !this.searchKeyword.trim()) {
            console.log("[VenueList] 搜索关键词为空，清空搜索结果");
            if (this.venueStore && this.venueStore.clearSearchResults) {
              this.venueStore.clearSearchResults();
            }
            return;
          }
          console.log("[VenueList] 输入变化，自动触发搜索:", this.searchKeyword);
          this.handleSearch();
        } catch (error) {
          console.error("[VenueList] 搜索输入处理失败:", error);
        }
      }, 800);
    },
    // 处理搜索
    async handleSearch() {
      const trimmedKeyword = this.searchKeyword ? this.searchKeyword.trim() : "";
      try {
        if (!trimmedKeyword) {
          this.venueStore.clearSearchResults();
          await this.venueStore.getVenueList({ page: 1, pageSize: 50, refresh: true });
          this.updatePagination();
          return;
        }
        const searchParams = {
          keyword: trimmedKeyword,
          page: 1,
          pageSize: 50
        };
        if (this.selectedType) {
          searchParams.type = this.selectedType;
        }
        if (this.filterOptions.minPrice) {
          searchParams.minPrice = Number(this.filterOptions.minPrice);
        }
        if (this.filterOptions.maxPrice) {
          searchParams.maxPrice = Number(this.filterOptions.maxPrice);
        }
        if (this.filterOptions.distance) {
          searchParams.distance = this.filterOptions.distance;
        }
        if (this.filterOptions.facilities && Array.isArray(this.filterOptions.facilities) && this.filterOptions.facilities.length > 0) {
          searchParams.facilities = this.filterOptions.facilities.join(",");
        }
        console.log("[VenueList] 发起搜索请求:", searchParams);
        const searchResult = await this.venueStore.searchVenues(searchParams);
        console.log("[VenueList] 搜索完成，结果数量:", (searchResult == null ? void 0 : searchResult.length) || 0);
        console.log("[VenueList] 当前 searchResults:", this.searchResults);
      } catch (error) {
        console.error("搜索失败:", error);
        common_vendor.index.showToast({
          title: "搜索失败，请重试",
          icon: "error"
        });
      }
    },
    // 选择场馆类型
    async selectType(typeId) {
      this.selectedType = typeId;
      if (typeId === "") {
        this.searchKeyword = "";
        this.resetFilter();
        this.venueStore.setSearchResults([]);
      }
      try {
        const params = {
          page: 1,
          pageSize: 50,
          refresh: true,
          ...this.filterOptions
        };
        if (typeId) {
          params.type = typeId;
        }
        console.log("选择场馆类型，参数:", params);
        await this.venueStore.getVenueList(params);
        this.updatePagination();
      } catch (error) {
        console.error("筛选失败:", error);
      }
    },
    // 跳转到详情页
    navigateToDetail(venueId) {
      common_vendor.index.navigateTo({
        url: `/pages/venue/detail?id=${venueId}`
      });
    },
    // 显示筛选弹窗
    showFilterModal() {
      const debugEnabled = this.enablePopupDebug || false;
      if (debugEnabled) {
        console.log("showFilterModal被调用");
      }
      this.internalPopupOpened = true;
      const sysInfo = {
        ...common_vendor.index.getWindowInfo(),
        ...common_vendor.index.getDeviceInfo(),
        ...common_vendor.index.getAppBaseInfo()
      };
      if (debugEnabled) {
        console.log("当前popup引用状态:", {
          cachedRef: !!this._popupRef,
          directRef: !!this.$refs.filterPopup,
          refType: this.$refs.filterPopup ? typeof this.$refs.filterPopup : "undefined",
          env: sysInfo.platform
        });
      }
      const openPopup = () => {
        let popup = null;
        popup = this.$refs.filterPopup;
        if (Array.isArray(popup)) {
          popup = popup[0];
          if (debugEnabled) {
            console.log("filterPopup是数组，使用第一个元素");
          }
        }
        if (!popup && this._popupRef) {
          popup = this._popupRef;
          if (debugEnabled) {
            console.log("使用缓存的弹窗引用");
          }
        }
        if (!popup && (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools")) {
          try {
            if (this.$scope && typeof this.$scope.selectComponent === "function") {
              const componentInstance = this.$scope.selectComponent("#filter-popup-component");
              if (debugEnabled && componentInstance !== null) {
                console.log("通过$scope.selectComponent获取到组件实例:", {
                  type: typeof componentInstance,
                  isNull: componentInstance === null,
                  isUndefined: componentInstance === void 0,
                  hasOpen: componentInstance && typeof componentInstance.open === "function",
                  hasClose: componentInstance && typeof componentInstance.close === "function",
                  keys: componentInstance ? Object.keys(componentInstance) : [],
                  prototype: componentInstance ? Object.getPrototypeOf(componentInstance) : null
                });
              }
              if (componentInstance && typeof componentInstance.open === "function") {
                popup = componentInstance;
                if (debugEnabled) {
                  console.log("$scope.selectComponent获取到有效的uni-popup实例");
                }
              } else if (componentInstance) {
                if (debugEnabled) {
                  console.log("尝试从组件实例中查找uni-popup子组件");
                }
                if (componentInstance.$refs && componentInstance.$refs.popup) {
                  popup = componentInstance.$refs.popup;
                  if (debugEnabled) {
                    console.log("通过组件实例的$refs.popup获取到uni-popup");
                  }
                } else if (typeof componentInstance.selectComponent === "function") {
                  try {
                    popup = componentInstance.selectComponent(".uni-popup");
                    if (debugEnabled) {
                      console.log("通过组件实例的selectComponent获取到uni-popup");
                    }
                  } catch (e) {
                    if (debugEnabled) {
                      console.log("组件实例selectComponent失败:", e);
                    }
                  }
                } else if (componentInstance.data && componentInstance.setData) {
                  if (debugEnabled) {
                    console.log("检测到微信小程序组件实例，尝试直接调用方法");
                  }
                  if (typeof componentInstance.open === "function") {
                    popup = componentInstance;
                  }
                }
              }
            }
          } catch (e) {
            if (debugEnabled) {
              console.log("$scope.selectComponent获取失败:", e);
            }
          }
        }
        if (popup) {
          if (debugEnabled) {
            console.log("准备打开弹窗，组件实例信息:", {
              type: typeof popup,
              hasOpen: typeof popup.open === "function",
              hasClose: typeof popup.close === "function",
              methods: Object.getOwnPropertyNames(popup).filter((name) => typeof popup[name] === "function"),
              constructor: popup.constructor ? popup.constructor.name : "unknown"
            });
          }
          if (typeof popup.open === "function") {
            try {
              popup.open("bottom");
              this._popupRef = popup;
              if (debugEnabled) {
                console.log("筛选弹窗打开成功");
              }
              if (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools") {
                setTimeout(() => {
                  this.applyPopupStyles();
                }, 50);
              }
              return true;
            } catch (e) {
              if (debugEnabled) {
                console.error("打开弹窗时发生错误:", e);
              }
              return false;
            }
          } else {
            if (debugEnabled) {
              console.error("组件实例没有open方法，尝试备选方案");
            }
            this.internalPopupOpened = true;
            this.$forceUpdate();
            setTimeout(() => {
              const popupEl = document.querySelector("#filter-popup-component");
              if (popupEl) {
                popupEl.style.display = "block";
                popupEl.style.zIndex = "999";
                if (debugEnabled) {
                  console.log("通过DOM操作强制显示弹窗");
                }
                return true;
              }
            }, 100);
            return false;
          }
        } else {
          if (debugEnabled) {
            console.error("filterPopup ref未找到或open未就绪(env: " + sysInfo.platform + "," + sysInfo.uniPlatform + "," + sysInfo.SDKVersion + ")");
          }
          return false;
        }
      };
      if (!openPopup()) {
        setTimeout(() => {
          if (!openPopup()) {
            if (debugEnabled) {
              console.error("弹窗打开失败，请检查组件是否正确渲染");
              common_vendor.index.showToast({
                title: "弹窗打开失败",
                icon: "none"
              });
            }
          }
        }, 100);
      }
    },
    // 强制创建弹窗
    forceCreatePopup() {
      const debugEnabled = this.enablePopupDebug || false;
      if (debugEnabled) {
        console.log("尝试强制创建弹窗");
      }
      const sysInfo = {
        ...common_vendor.index.getWindowInfo(),
        ...common_vendor.index.getDeviceInfo(),
        ...common_vendor.index.getAppBaseInfo()
      };
      common_vendor.index.showToast({
        title: "正在准备筛选...",
        icon: "none",
        duration: 1500
      });
      this.$forceUpdate();
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#filter-popup-component").boundingClientRect((data) => {
        if (debugEnabled) {
          console.log("通过选择器查询结果:", data);
        }
        if (data) {
          this.popupPosition = {
            ...this.popupPosition,
            forceCreateRect: data,
            timestamp: Date.now()
          };
        }
      }).exec();
      setTimeout(() => {
        let popup = null;
        popup = this.$refs.filterPopup;
        if (Array.isArray(popup)) {
          popup = popup[0];
          if (debugEnabled) {
            console.log("forceCreatePopup: filterPopup是数组，使用第一个元素");
          }
        }
        if (!popup && this._popupRef) {
          popup = this._popupRef;
          if (debugEnabled) {
            console.log("forceCreatePopup: 使用缓存的弹窗引用");
          }
        }
        if (!popup && (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools")) {
          try {
            if (this.$scope && typeof this.$scope.selectComponent === "function") {
              popup = this.$scope.selectComponent("#filter-popup-component");
              if (debugEnabled) {
                console.log("forceCreatePopup: 通过$scope.selectComponent获取到弹窗组件实例:", typeof popup);
              }
            }
          } catch (e) {
            if (debugEnabled) {
              console.log("forceCreatePopup: $scope.selectComponent获取失败:", e);
            }
          }
        }
        if (popup && typeof popup.open === "function") {
          try {
            popup.open("bottom");
            this._popupRef = popup;
            this.internalPopupOpened = true;
            if (debugEnabled) {
              console.log("forceCreatePopup: 弹窗打开成功");
            }
            setTimeout(() => {
              this.applyPopupStyles();
              if (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools") {
                try {
                  const styleId = "filter-popup-fix-style";
                  let styleEl = document.getElementById(styleId);
                  if (!styleEl) {
                    styleEl = document.createElement("style");
                    styleEl.id = styleId;
                    document.head.appendChild(styleEl);
                  }
                  styleEl.innerHTML = `
                    #uni-popup-bottom {
                      bottom: 0 !important;
                      left: 0 !important;
                      right: 0 !important;
                      position: fixed !important;
                      z-index: 999 !important;
                    }
                    .uni-popup-bottom {
                      bottom: 0 !important;
                      left: 0 !important;
                      right: 0 !important;
                      position: fixed !important;
                      z-index: 999 !important;
                    }
                  `;
                } catch (styleError) {
                  if (debugEnabled) {
                    console.error("应用强制样式失败:", styleError);
                  }
                }
              }
            }, 100);
          } catch (e) {
            if (debugEnabled) {
              console.error("forceCreatePopup: 打开弹窗时发生错误:", e);
            }
            this.fallbackToRefsMethod();
          }
        } else {
          if (debugEnabled) {
            console.log("forceCreatePopup: 获取到的组件实例没有open方法或为空");
          }
          this.fallbackToRefsMethod();
        }
      }, 200);
    },
    // 回退到$refs方式的方法
    fallbackToRefsMethod() {
      const debugEnabled = this.enablePopupDebug || false;
      if (debugEnabled) {
        console.log("fallbackToRefsMethod: 开始回退到$refs方式");
      }
      let popup = null;
      if (this._popupRef && typeof this._popupRef.open === "function") {
        popup = this._popupRef;
        if (debugEnabled) {
          console.log("fallbackToRefsMethod: 使用缓存的弹窗引用");
        }
      } else if (this.$refs.filterPopup) {
        if (Array.isArray(this.$refs.filterPopup)) {
          popup = this.$refs.filterPopup[0];
          if (debugEnabled) {
            console.log("fallbackToRefsMethod: 使用$refs数组中的第一个弹窗引用");
          }
        } else {
          popup = this.$refs.filterPopup;
          if (debugEnabled) {
            console.log("fallbackToRefsMethod: 使用$refs弹窗引用");
          }
        }
      }
      if (popup && typeof popup.open === "function") {
        popup.open("bottom");
        this._popupRef = popup;
        this.internalPopupOpened = true;
        if (debugEnabled) {
          console.log("fallbackToRefsMethod: 成功打开弹窗");
        }
        setTimeout(() => {
          this.applyPopupStyles();
        }, 50);
      } else {
        if (debugEnabled) {
          console.log("fallbackToRefsMethod: 所有方式都失败，弹窗引用不可用");
          common_vendor.index.showToast({
            title: "弹窗未就绪，请稍后再试",
            icon: "none"
          });
        }
      }
    },
    closeFilterModal() {
      const debugEnabled = this.enablePopupDebug || false;
      if (debugEnabled) {
        console.log("closeFilterModal被调用");
      }
      this.internalPopupOpened = false;
      const sysInfo = {
        ...common_vendor.index.getWindowInfo(),
        ...common_vendor.index.getDeviceInfo(),
        ...common_vendor.index.getAppBaseInfo()
      };
      if (debugEnabled) {
        console.log("关闭时popup引用状态:", {
          cachedRef: !!this._popupRef,
          directRef: !!this.$refs.filterPopup,
          refType: this.$refs.filterPopup ? typeof this.$refs.filterPopup : "undefined",
          env: sysInfo.platform
        });
      }
      const tryClosePopup = (retryCount = 0) => {
        let popup = null;
        popup = this.$refs.filterPopup;
        if (Array.isArray(popup)) {
          popup = popup[0];
          if (debugEnabled) {
            console.log("关闭弹窗: filterPopup是数组，使用第一个元素");
          }
        }
        if (!popup && this._popupRef) {
          popup = this._popupRef;
          if (debugEnabled) {
            console.log("关闭弹窗: 使用缓存的弹窗引用");
          }
        }
        if (!popup && (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools")) {
          try {
            if (this.$scope && typeof this.$scope.selectComponent === "function") {
              const componentInstance = this.$scope.selectComponent("#filter-popup-component");
              if (debugEnabled) {
                console.log("关闭弹窗: 通过$scope.selectComponent获取到组件实例:", {
                  type: typeof componentInstance,
                  isNull: componentInstance === null,
                  isUndefined: componentInstance === void 0,
                  hasOpen: componentInstance && typeof componentInstance.open === "function",
                  hasClose: componentInstance && typeof componentInstance.close === "function",
                  keys: componentInstance ? Object.keys(componentInstance) : []
                });
              }
              if (componentInstance === null) {
                if (debugEnabled) {
                  console.log("关闭弹窗: $scope.selectComponent返回null，跳过后续检查");
                }
              } else if (componentInstance && typeof componentInstance.close === "function") {
                popup = componentInstance;
                if (debugEnabled) {
                  console.log("关闭弹窗: $scope.selectComponent获取到有效的uni-popup实例");
                }
              } else if (componentInstance) {
                if (debugEnabled) {
                  console.log("关闭弹窗: 尝试从组件实例中查找uni-popup子组件");
                }
                if (componentInstance.$refs && componentInstance.$refs.popup) {
                  popup = componentInstance.$refs.popup;
                  if (debugEnabled) {
                    console.log("关闭弹窗: 通过组件实例的$refs.popup获取到uni-popup");
                  }
                } else if (typeof componentInstance.selectComponent === "function") {
                  try {
                    popup = componentInstance.selectComponent(".uni-popup");
                    if (debugEnabled) {
                      console.log("关闭弹窗: 通过组件实例的selectComponent获取到uni-popup");
                    }
                  } catch (e) {
                    if (debugEnabled) {
                      console.log("关闭弹窗: 组件实例selectComponent失败:", e);
                    }
                  }
                }
              }
            }
          } catch (e) {
            if (debugEnabled) {
              console.log("关闭弹窗: $scope.selectComponent获取失败:", e);
            }
          }
        }
        if (popup) {
          this._popupRef = popup;
        }
        if (debugEnabled) {
          console.log(`第${retryCount + 1}次尝试关闭弹窗:`, {
            popupExists: !!popup,
            hasCloseMethod: popup && typeof popup.close === "function",
            popupType: popup ? Array.isArray(popup) ? "array" : typeof popup : "undefined",
            methods: popup ? Object.getOwnPropertyNames(popup).filter((name) => typeof popup[name] === "function") : [],
            constructor: popup && popup.constructor ? popup.constructor.name : "unknown"
          });
        }
        if (popup && typeof popup.close === "function") {
          try {
            popup.close();
            this.internalPopupOpened = false;
            if (debugEnabled) {
              console.log("筛选弹窗关闭成功");
            }
            return true;
          } catch (e) {
            if (debugEnabled) {
              console.error("关闭弹窗失败:", e);
            }
            return false;
          }
        } else if (popup) {
          if (debugEnabled) {
            console.log("组件实例没有close方法，尝试备选方案");
          }
          this.internalPopupOpened = false;
          this.$forceUpdate();
          setTimeout(() => {
            const popupEl = document.querySelector("#filter-popup-component");
            if (popupEl) {
              popupEl.style.display = "none";
              if (debugEnabled) {
                console.log("通过DOM操作强制隐藏弹窗");
              }
            }
          }, 50);
          if (debugEnabled) {
            console.log("使用备选方案关闭弹窗");
          }
          return true;
        } else {
          if (debugEnabled) {
            console.error(`关闭弹窗失败: ref未找到或close方法未就绪 (尝试${retryCount + 1}次)(env: ${sysInfo.platform},${sysInfo.uniPlatform},${sysInfo.SDKVersion})`);
          }
          return false;
        }
      };
      if (!tryClosePopup(0)) {
        setTimeout(() => {
          if (!tryClosePopup(1)) {
            if (debugEnabled) {
              console.error("多次尝试关闭弹窗失败，强制更新内部状态");
            }
            this.internalPopupOpened = false;
            if (sysInfo.platform === "mp-weixin" || sysInfo.platform === "devtools") {
              const query = common_vendor.index.createSelectorQuery().in(this);
              query.select("#filter-popup-component").boundingClientRect((data) => {
                if (data) {
                  if (debugEnabled) {
                    console.log("找到弹窗元素，尝试强制隐藏");
                  }
                  this.$forceUpdate();
                }
              }).exec();
            }
            if (debugEnabled) {
              common_vendor.index.showToast({
                title: "关闭弹窗遇到问题",
                icon: "none",
                duration: 1500
              });
            }
          }
        }, 100);
      }
    },
    // 应用弹窗样式，确保在微信小程序中正确显示
    applyPopupStyles() {
      try {
        console.log("尝试应用弹窗样式");
        const sysInfo = {
          ...common_vendor.index.getWindowInfo(),
          ...common_vendor.index.getDeviceInfo(),
          ...common_vendor.index.getAppBaseInfo()
        };
        this.popupPosition = {
          platform: sysInfo.platform,
          windowHeight: sysInfo.windowHeight,
          windowWidth: sysInfo.windowWidth,
          pixelRatio: sysInfo.pixelRatio,
          timestamp: Date.now()
        };
        setTimeout(() => {
          const query = common_vendor.index.createSelectorQuery().in(this);
          query.select(".uni-popup").boundingClientRect((data) => {
            console.log("弹窗元素位置信息:", data);
            if (data) {
              this.popupPosition.popupRect = data;
            }
          }).exec();
        }, 100);
      } catch (e) {
        console.error("应用弹窗样式失败:", e);
      }
    },
    // 处理弹窗状态变化
    popupChange(e) {
      const sysInfo = {
        ...common_vendor.index.getWindowInfo(),
        ...common_vendor.index.getDeviceInfo(),
        ...common_vendor.index.getAppBaseInfo()
      };
      console.log("弹窗状态变化:", e, "当前环境:", sysInfo.platform);
      if (e.show === false) {
        this.internalPopupOpened = false;
        console.log("弹窗已关闭，更新内部状态为false");
      } else if (e.show === true) {
        this.internalPopupOpened = true;
        console.log("弹窗已打开，更新内部状态为true");
        let popup = this.$refs.filterPopup;
        if (Array.isArray(popup))
          popup = popup[0];
        if (popup) {
          this._popupRef = popup;
          console.log("弹窗打开成功，缓存弹窗引用:", {
            popupType: typeof popup,
            isArray: Array.isArray(popup),
            hasOpen: typeof popup.open === "function",
            hasClose: typeof popup.close === "function",
            platform: sysInfo.platform,
            uniPlatform: sysInfo.uniPlatform
          });
          if (sysInfo.platform === "devtools" || sysInfo.platform === "mp-weixin") {
            this.applyPopupStyles();
          }
          setTimeout(() => {
            const popupWrapper = document.getElementById("popup-wrapper");
            if (popupWrapper) {
              console.log("找到弹窗容器，确保位置正确");
              if (sysInfo.platform === "mp-weixin") {
                this.applyPopupStyles();
              }
            }
          }, 50);
        } else {
          console.error("弹窗打开但无法获取引用");
          setTimeout(() => {
            const delayedPopup = this.$refs.filterPopup;
            if (delayedPopup) {
              this._popupRef = delayedPopup;
              console.log("延迟获取弹窗引用成功");
              this.applyPopupStyles();
            }
          }, 100);
        }
      }
    },
    // 应用弹窗样式（解决微信小程序中的定位问题）
    applyPopupStyles() {
      try {
        const sysInfo = {
          ...common_vendor.index.getWindowInfo(),
          ...common_vendor.index.getDeviceInfo(),
          ...common_vendor.index.getAppBaseInfo()
        };
        console.log("应用弹窗样式，当前环境:", sysInfo.platform);
        if (sysInfo.platform !== "mp-weixin" && sysInfo.platform !== "devtools") {
          return;
        }
        this.popupPosition = {
          platform: sysInfo.platform,
          windowHeight: sysInfo.windowHeight,
          windowWidth: sysInfo.windowWidth,
          statusBarHeight: sysInfo.statusBarHeight,
          safeAreaInsets: sysInfo.safeAreaInsets || {}
        };
        setTimeout(() => {
          const query = common_vendor.index.createSelectorQuery().in(this);
          query.select("#filter-popup-component").boundingClientRect((data) => {
            if (data) {
              console.log("通过选择器查询到弹窗元素:", data);
              this.popupPosition.popupRect = data;
              let popup = this.$refs.filterPopup;
              if (Array.isArray(popup)) {
                popup = popup[0];
              }
              if (!popup && this._popupRef) {
                popup = this._popupRef;
              }
              const popupExists = !!popup;
              const hasOpenMethod = popup && typeof popup.open === "function";
              console.log("应用样式状态:", {
                popupExists,
                hasOpenMethod,
                popupType: typeof popup,
                platform: sysInfo.platform,
                uniPlatform: sysInfo.uniPlatform
              });
              if (popupExists && hasOpenMethod) {
                console.log("应用弹窗样式成功");
              } else {
                console.log("应用弹窗样式失败，无法获取弹窗引用");
              }
            } else {
              console.log("未找到弹窗元素");
            }
          }).exec();
        }, 100);
      } catch (e) {
        console.error("应用弹窗样式失败:", e);
      }
    },
    // 选择距离
    selectDistance(distance) {
      this.filterOptions.distance = distance;
    },
    // 切换设施选择
    toggleFacility(facility) {
      if (!Array.isArray(this.filterOptions.facilities)) {
        this.filterOptions.facilities = [];
      }
      const index = this.filterOptions.facilities.indexOf(facility);
      if (index > -1) {
        this.filterOptions.facilities.splice(index, 1);
      } else {
        this.filterOptions.facilities.push(facility);
      }
    },
    // 重置筛选
    resetFilter() {
      this.filterOptions = {
        minPrice: "",
        maxPrice: "",
        distance: "",
        facilities: []
      };
    },
    // 应用筛选
    async applyFilter() {
      this.closeFilterModal();
      try {
        if (this.searchKeyword && this.searchKeyword.trim()) {
          const params = {
            keyword: this.searchKeyword.trim()
          };
          if (this.selectedType) {
            params.type = this.selectedType;
          }
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice);
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice);
          }
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance;
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(",");
          }
          console.log("应用筛选（搜索模式），参数:", params);
          await this.venueStore.searchVenues(params);
        } else {
          const params = {
            page: 1,
            pageSize: 50,
            refresh: true
          };
          if (this.selectedType) {
            params.type = this.selectedType;
          }
          if (this.filterOptions.minPrice) {
            params.minPrice = Number(this.filterOptions.minPrice);
          }
          if (this.filterOptions.maxPrice) {
            params.maxPrice = Number(this.filterOptions.maxPrice);
          }
          if (this.filterOptions.distance) {
            params.distance = this.filterOptions.distance;
          }
          if (this.filterOptions.facilities && this.filterOptions.facilities.length > 0) {
            params.facilities = this.filterOptions.facilities.join(",");
          }
          console.log("应用筛选（列表模式），参数:", params);
          await this.venueStore.getVenueList(params);
          this.updatePagination();
        }
      } catch (error) {
        console.error("应用筛选失败:", error);
        common_vendor.index.showToast({
          title: "筛选失败，请重试",
          icon: "error"
        });
      }
    },
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        "AVAILABLE": "status-available",
        "MAINTENANCE": "status-maintenance",
        "OCCUPIED": "status-occupied"
      };
      return statusMap[status] || "status-available";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "AVAILABLE": "可预约",
        "MAINTENANCE": "维护中",
        "OCCUPIED": "已满"
      };
      return statusMap[status] || "可预约";
    },
    // 缓存优化的刷新方法
    async refreshDataWithCache() {
      const now = Date.now();
      if (now - this.lastRefreshTime < this.cacheTimeout && !this.isRefreshing) {
        console.log("使用缓存数据，跳过刷新");
        return;
      }
      if (this.isRefreshing) {
        console.log("正在刷新中，跳过重复请求");
        return;
      }
      try {
        this.isRefreshing = true;
        console.log("开始缓存优化刷新...");
        const shouldRefresh = !this.venueList.length || !this.venueTypes.length || now - this.lastRefreshTime > this.cacheTimeout;
        if (shouldRefresh) {
          common_vendor.index.showLoading({
            title: "正在刷新数据..."
          });
          const refreshParams = {
            page: 1,
            pageSize: 50,
            refresh: true,
            compress: true
          };
          const refreshStartTime = Date.now();
          await Promise.all([
            this.venueStore.getVenueList(refreshParams),
            this.venueStore.getVenueTypes()
          ]);
          const refreshDuration = Date.now() - refreshStartTime;
          this.lastRefreshTime = now;
          try {
            common_vendor.index.setStorageSync("venue_list_cache", {
              venueList: this.venueList,
              venueTypes: this.venueTypes,
              timestamp: now
            });
          } catch (e) {
            console.warn("缓存存储失败:", e);
          }
          console.log("缓存刷新完成");
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "数据刷新完成",
            icon: "success",
            duration: 1500
          });
        } else {
          console.log("数据仍在缓存有效期内");
        }
        this.updatePagination();
      } catch (error) {
        console.error("缓存刷新失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据刷新失败",
          icon: "error"
        });
      } finally {
        this.isRefreshing = false;
      }
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  _component_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: $data.selectedType === "" ? 1 : "",
    f: common_vendor.o(($event) => $options.selectType("")),
    g: common_vendor.f($options.venueTypes && Array.isArray($options.venueTypes) ? $options.venueTypes : [], (type, k0, i0) => {
      return {
        a: common_vendor.t(type.name),
        b: type.id,
        c: $data.selectedType === type.id ? 1 : "",
        d: common_vendor.o(($event) => $options.selectType(type.id), type.id)
      };
    }),
    h: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args)),
    i: common_vendor.f($options.filteredVenues && Array.isArray($options.filteredVenues) ? $options.filteredVenues : [], (venue, k0, i0) => {
      return common_vendor.e({
        a: venue.image || "/static/default-venue.jpg",
        b: common_vendor.t(venue.name || "未知场馆"),
        c: common_vendor.t(venue.rating || "4.5"),
        d: common_vendor.t(venue.location || "位置未知"),
        e: common_vendor.t(venue.type || "运动场馆"),
        f: venue.supportSharing
      }, venue.supportSharing ? {} : {}, {
        g: common_vendor.t(venue.price || 0),
        h: common_vendor.t($options.getStatusText(venue.status)),
        i: common_vendor.n($options.getStatusClass(venue.status)),
        j: venue.id,
        k: common_vendor.o(($event) => $options.navigateToDetail(venue.id), venue.id)
      });
    }),
    j: (!$options.filteredVenues || !Array.isArray($options.filteredVenues) || $options.filteredVenues.length === 0) && !$options.loading
  }, (!$options.filteredVenues || !Array.isArray($options.filteredVenues) || $options.filteredVenues.length === 0) && !$options.loading ? {
    k: common_vendor.t($data.searchKeyword ? "未找到相关场馆" : "暂无场馆数据")
  } : {}, {
    l: $options.hasMore
  }, $options.hasMore ? {
    m: common_vendor.t($options.loading ? "加载中..." : "加载更多"),
    n: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    o: common_vendor.o((...args) => $options.closeFilterModal && $options.closeFilterModal(...args)),
    p: $data.filterOptions.minPrice,
    q: common_vendor.o(($event) => $data.filterOptions.minPrice = $event.detail.value),
    r: $data.filterOptions.maxPrice,
    s: common_vendor.o(($event) => $data.filterOptions.maxPrice = $event.detail.value),
    t: common_vendor.f($data.distanceOptions && Array.isArray($data.distanceOptions) ? $data.distanceOptions : [], (distance, k0, i0) => {
      return {
        a: common_vendor.t(distance.label),
        b: distance.value,
        c: $data.filterOptions.distance === distance.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectDistance(distance.value), distance.value)
      };
    }),
    v: common_vendor.f($data.facilityOptions && Array.isArray($data.facilityOptions) ? $data.facilityOptions : [], (facility, k0, i0) => {
      return {
        a: common_vendor.t(facility.label),
        b: facility.value,
        c: ($data.filterOptions.facilities && Array.isArray($data.filterOptions.facilities) ? $data.filterOptions.facilities : []).includes(facility.value) ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleFacility(facility.value), facility.value)
      };
    }),
    w: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    x: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args)),
    y: $data.internalPopupOpened,
    z: common_vendor.sr("filterPopup", "17cb4dd5-0"),
    A: common_vendor.o($options.popupChange),
    B: common_vendor.p({
      id: "filter-popup-component",
      type: "bottom",
      ["mask-click"]: false,
      ["safe-area"]: false,
      animation: true,
      ["background-color"]: "#fff",
      ["custom-style"]: {
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999
      }
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-17cb4dd5"]]);
wx.createPage(MiniProgramPage);
