/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-ebed24a8 {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);
  padding: 60rpx 60rpx 40rpx;
  display: flex;
  flex-direction: column;
}
.header.data-v-ebed24a8 {
  text-align: center;
  margin-bottom: 80rpx;
}
.header .logo.data-v-ebed24a8 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}
.header .app-name.data-v-ebed24a8 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 16rpx;
}
.header .app-slogan.data-v-ebed24a8 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.login-form.data-v-ebed24a8 {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.login-form .tab-bar.data-v-ebed24a8 {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 40rpx;
}
.login-form .tab-bar .tab-item.data-v-ebed24a8 {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}
.login-form .tab-bar .tab-item.active.data-v-ebed24a8 {
  background-color: #ffffff;
  color: #ff6b35;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.login-form .form-item.data-v-ebed24a8 {
  margin-bottom: 30rpx;
}
.login-form .form-item .input-wrapper.data-v-ebed24a8 {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 88rpx;
}
.login-form .form-item .input-wrapper .input-icon.data-v-ebed24a8 {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}
.login-form .form-item .input-wrapper .input-field.data-v-ebed24a8 {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
}
.login-form .form-item .input-wrapper .password-toggle.data-v-ebed24a8 {
  font-size: 32rpx;
  opacity: 0.6;
  padding: 8rpx;
}
.login-form .form-item .input-wrapper .sms-btn.data-v-ebed24a8 {
  padding: 12rpx 24rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.login-form .form-item .input-wrapper .sms-btn[disabled].data-v-ebed24a8 {
  background-color: #cccccc;
  color: #ffffff;
}
.login-form .login-btn.data-v-ebed24a8 {
  width: 100%;
  height: 88rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}
.login-form .login-btn[disabled].data-v-ebed24a8 {
  background-color: #cccccc;
  color: #ffffff;
}
.login-form .forgot-password.data-v-ebed24a8 {
  text-align: right;
}
.login-form .forgot-password text.data-v-ebed24a8 {
  font-size: 26rpx;
  color: #ff6b35;
}
.other-login.data-v-ebed24a8 {
  margin-bottom: 40rpx;
}
.other-login .divider.data-v-ebed24a8 {
  position: relative;
  text-align: center;
  margin-bottom: 30rpx;
}
.other-login .divider.data-v-ebed24a8::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.3);
}
.other-login .divider .divider-text.data-v-ebed24a8 {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f65 100%);
  padding: 0 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}
.other-login .social-login.data-v-ebed24a8 {
  display: flex;
  justify-content: center;
  gap: 60rpx;
}
.other-login .social-login .social-item.data-v-ebed24a8 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.other-login .social-login .social-item .social-icon.data-v-ebed24a8 {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 12rpx;
}
.other-login .social-login .social-item .social-text.data-v-ebed24a8 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.footer.data-v-ebed24a8 {
  text-align: center;
  margin-bottom: 30rpx;
}
.footer .footer-text.data-v-ebed24a8 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.footer .footer-link.data-v-ebed24a8 {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
  margin-left: 8rpx;
}
.agreement.data-v-ebed24a8 {
  text-align: center;
}
.agreement .agreement-text.data-v-ebed24a8 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}
.agreement .agreement-link.data-v-ebed24a8 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}