/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-91798fc9 {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.image-section.data-v-91798fc9 {
  position: relative;
  height: 500rpx;
}
.image-section .venue-swiper.data-v-91798fc9 {
  height: 100%;
}
.image-section .venue-swiper .venue-image.data-v-91798fc9 {
  width: 100%;
  height: 100%;
}
.image-section .back-btn.data-v-91798fc9 {
  position: absolute;
  top: 60rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  z-index: 10;
}
.info-section.data-v-91798fc9 {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.info-section .venue-header.data-v-91798fc9 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.info-section .venue-header .venue-name.data-v-91798fc9 {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 20rpx;
}
.info-section .venue-header .venue-rating.data-v-91798fc9 {
  display: flex;
  align-items: center;
}
.info-section .venue-header .venue-rating .rating-score.data-v-91798fc9 {
  font-size: 28rpx;
  color: #ff6b35;
  margin-right: 8rpx;
}
.info-section .venue-header .venue-rating .rating-star.data-v-91798fc9 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.info-section .venue-header .venue-rating .rating-count.data-v-91798fc9 {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-location.data-v-91798fc9 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.info-section .venue-location .location-icon.data-v-91798fc9 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.info-section .venue-location .location-text.data-v-91798fc9 {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
}
.info-section .venue-location .distance-text.data-v-91798fc9 {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-price.data-v-91798fc9 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.info-section .venue-price .price-label.data-v-91798fc9 {
  font-size: 28rpx;
  color: #333333;
}
.info-section .venue-price .price-value.data-v-91798fc9 {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
  margin: 0 8rpx;
}
.info-section .venue-price .price-unit.data-v-91798fc9 {
  font-size: 24rpx;
  color: #999999;
}
.info-section .venue-tags.data-v-91798fc9 {
  display: flex;
  flex-wrap: wrap;
}
.info-section .venue-tags .venue-tag.data-v-91798fc9 {
  font-size: 22rpx;
  color: #666666;
  background-color: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.description-section.data-v-91798fc9,
.facilities-section.data-v-91798fc9,
.hours-section.data-v-91798fc9 {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.description-section .section-title.data-v-91798fc9,
.facilities-section .section-title.data-v-91798fc9,
.hours-section .section-title.data-v-91798fc9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.description-text.data-v-91798fc9 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.facilities-grid.data-v-91798fc9 {
  display: flex;
  flex-wrap: wrap;
}
.facilities-grid .facility-item.data-v-91798fc9 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.facilities-grid .facility-item .facility-icon.data-v-91798fc9 {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}
.facilities-grid .facility-item .facility-name.data-v-91798fc9 {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}
.hours-info .hours-text.data-v-91798fc9 {
  font-size: 28rpx;
  color: #666666;
}
.timeslot-section.data-v-91798fc9 {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.timeslot-section .section-header.data-v-91798fc9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.timeslot-section .section-header .section-title.data-v-91798fc9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.timeslot-section .section-header .refresh-btn.data-v-91798fc9 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
}
.timeslot-section .section-header .refresh-btn.data-v-91798fc9:active {
  background-color: #e8e8e8;
}
.timeslot-section .section-header .refresh-btn .refresh-icon.data-v-91798fc9 {
  margin-right: 8rpx;
  font-size: 28rpx;
}
.timeslot-section .section-header .refresh-btn .refresh-text.data-v-91798fc9 {
  font-size: 24rpx;
}
.timeslot-section .date-selector.data-v-91798fc9 {
  margin-bottom: 30rpx;
}
.timeslot-section .date-selector .date-scroll.data-v-91798fc9 {
  white-space: nowrap;
}
.timeslot-section .date-selector .date-scroll .date-item.data-v-91798fc9 {
  display: inline-block;
  text-align: center;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  min-width: 120rpx;
}
.timeslot-section .date-selector .date-scroll .date-item.active.data-v-91798fc9 {
  background-color: #ff6b35;
  color: #ffffff;
}
.timeslot-section .date-selector .date-scroll .date-item .date-day.data-v-91798fc9 {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}
.timeslot-section .date-selector .date-scroll .date-item .date-date.data-v-91798fc9 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
}
.timeslot-section .timeslot-list .timeslot-item.data-v-91798fc9 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  position: relative;
}
.timeslot-section .timeslot-list .timeslot-item.selected.data-v-91798fc9 {
  background-color: #fff7f0;
  border-color: #ff6b35;
}
.timeslot-section .timeslot-list .timeslot-item.occupied.data-v-91798fc9 {
  background-color: #f5f5f5;
  opacity: 0.6;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-status.data-v-91798fc9 {
  color: #999999;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-time.data-v-91798fc9 {
  color: #999999;
}
.timeslot-section .timeslot-list .timeslot-item.occupied .slot-price.data-v-91798fc9 {
  color: #cccccc;
}
.timeslot-section .timeslot-list .timeslot-item.maintenance.data-v-91798fc9 {
  background-color: #fff7e6;
  opacity: 0.8;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.maintenance .slot-status.data-v-91798fc9 {
  color: #ff9500;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.expired.data-v-91798fc9 {
  background-color: #f0f0f0;
  opacity: 0.5;
  cursor: not-allowed;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-status.data-v-91798fc9 {
  color: #999999;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-time.data-v-91798fc9 {
  color: #cccccc;
  text-decoration: line-through;
}
.timeslot-section .timeslot-list .timeslot-item.expired .slot-price.data-v-91798fc9 {
  color: #cccccc;
  text-decoration: line-through;
}
.timeslot-section .timeslot-list .timeslot-item.disabled.data-v-91798fc9 {
  pointer-events: none;
}
.timeslot-section .timeslot-list .timeslot-item.disabled.data-v-91798fc9::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
}
.timeslot-section .timeslot-list .timeslot-item .slot-time.data-v-91798fc9 {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.timeslot-section .timeslot-list .timeslot-item .slot-price.data-v-91798fc9 {
  margin-right: 30rpx;
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}
.timeslot-section .timeslot-list .timeslot-item .slot-status.data-v-91798fc9 {
  font-size: 24rpx;
  color: #666666;
}
.timeslot-section .booking-type-section.data-v-91798fc9 {
  margin-bottom: 32rpx;
}
.timeslot-section .booking-type-section .booking-type-options.data-v-91798fc9 {
  margin-top: 24rpx;
}
.timeslot-section .booking-type-section .radio-item.data-v-91798fc9 {
  display: block;
  margin-bottom: 24rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-wrapper.data-v-91798fc9 {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.timeslot-section .booking-type-section .radio-item .radio-wrapper.data-v-91798fc9:active {
  background-color: #f0f0f0;
}
.timeslot-section .booking-type-section .radio-item .radio-circle.data-v-91798fc9 {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-circle.active.data-v-91798fc9 {
  border-color: #ff6b35;
  background-color: #ff6b35;
}
.timeslot-section .booking-type-section .radio-item .radio-circle .radio-dot.data-v-91798fc9 {
  width: 16rpx;
  height: 16rpx;
  background-color: white;
  border-radius: 50%;
}
.timeslot-section .booking-type-section .radio-item .radio-content.data-v-91798fc9 {
  flex: 1;
}
.timeslot-section .booking-type-section .radio-item .radio-content .radio-title.data-v-91798fc9 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.timeslot-section .booking-type-section .radio-item .radio-content .radio-desc.data-v-91798fc9 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.timeslot-section .booking-type-section .shared-form.data-v-91798fc9 {
  margin-top: 32rpx;
  padding: 32rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}
.timeslot-section .booking-type-section .shared-form .form-item.data-v-91798fc9 {
  margin-bottom: 32rpx;
}
.timeslot-section .booking-type-section .shared-form .form-item.data-v-91798fc9:last-child {
  margin-bottom: 0;
}
.timeslot-section .booking-type-section .shared-form .form-item .item-label.data-v-91798fc9 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.timeslot-section .booking-type-section .shared-form .form-item .form-input.data-v-91798fc9 {
  width: 100%;
  padding: 24rpx;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}
.timeslot-section .booking-type-section .shared-form .form-item .form-input.data-v-91798fc9:focus {
  border-color: #ff6b35;
}
.timeslot-section .booking-type-section .shared-form .form-item .picker-text.data-v-91798fc9 {
  padding: 24rpx;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.timeslot-section .booking-type-section .shared-form .form-item .picker-text.data-v-91798fc9::after {
  content: ">";
  color: #999;
  font-size: 24rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice.data-v-91798fc9 {
  background-color: #fff3e0;
  border: 2rpx solid #ffcc80;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-title.data-v-91798fc9 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #e65100;
  margin-bottom: 16rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-text.data-v-91798fc9 {
  display: block;
  font-size: 26rpx;
  color: #bf360c;
  line-height: 1.5;
  margin-bottom: 8rpx;
}
.timeslot-section .booking-type-section .shared-form .sharing-notice .notice-text.data-v-91798fc9:last-child {
  margin-bottom: 0;
}
.timeslot-section .booking-type-section .shared-form .time-notice.data-v-91798fc9 {
  background-color: #e3f2fd;
  border: 2rpx solid #90caf9;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
}
.timeslot-section .booking-type-section .shared-form .time-notice .notice-text.data-v-91798fc9 {
  font-size: 26rpx;
  color: #1565c0;
  line-height: 1.4;
}
.bottom-actions.data-v-91798fc9 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}
.bottom-actions .selected-info.data-v-91798fc9 {
  padding: 20rpx 30rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.bottom-actions .selected-info .selected-time.data-v-91798fc9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bottom-actions .selected-info .selected-time .time-text.data-v-91798fc9 {
  font-size: 26rpx;
  color: #666666;
}
.bottom-actions .selected-info .selected-time .price-text.data-v-91798fc9 {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}
.bottom-actions .action-buttons.data-v-91798fc9 {
  display: flex;
  padding: 20rpx 30rpx;
  align-items: center;
}
.bottom-actions .action-buttons .contact-btn.data-v-91798fc9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 20rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
}
.bottom-actions .action-buttons .contact-btn .contact-icon.data-v-91798fc9 {
  font-size: 28rpx;
  margin-bottom: 6rpx;
}
.bottom-actions .action-buttons .contact-btn .contact-text.data-v-91798fc9 {
  font-size: 20rpx;
  color: #666666;
}
.bottom-actions .action-buttons .book-btn.data-v-91798fc9 {
  flex: 1;
  height: 88rpx;
  background-color: #cccccc;
  color: #999999;
  border: none;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.bottom-actions .action-buttons .book-btn .book-btn-icon.data-v-91798fc9 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.bottom-actions .action-buttons .book-btn .book-btn-text.data-v-91798fc9 {
  font-size: 32rpx;
  font-weight: 600;
}
.bottom-actions .action-buttons .book-btn.book-btn-active.data-v-91798fc9 {
  background-color: #ff6b35;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}
.bottom-actions .action-buttons .book-btn.book-btn-active.data-v-91798fc9:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.bottom-actions .action-buttons .book-btn.data-v-91798fc9:disabled {
  background-color: #cccccc;
  color: #999999;
  box-shadow: none;
  -webkit-transform: none;
          transform: none;
}
.booking-modal.data-v-91798fc9 {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}
.booking-modal .modal-header.data-v-91798fc9 {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.booking-modal .modal-header .modal-title.data-v-91798fc9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.booking-modal .booking-info.data-v-91798fc9 {
  padding: 30rpx;
}
.booking-modal .booking-info .info-item.data-v-91798fc9 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.booking-modal .booking-info .info-item.data-v-91798fc9:last-child {
  margin-bottom: 0;
}
.booking-modal .booking-info .info-item .info-label.data-v-91798fc9 {
  font-size: 28rpx;
  color: #666666;
}
.booking-modal .booking-info .info-item .info-value.data-v-91798fc9 {
  font-size: 28rpx;
  color: #333333;
}
.booking-modal .booking-info .info-item .info-value.price.data-v-91798fc9 {
  color: #ff6b35;
  font-weight: 600;
}
.booking-modal .modal-actions.data-v-91798fc9 {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}
.booking-modal .modal-actions .cancel-btn.data-v-91798fc9,
.booking-modal .modal-actions .confirm-btn.data-v-91798fc9 {
  flex: 1;
  height: 100rpx;
  border: none;
  font-size: 28rpx;
}
.booking-modal .modal-actions .cancel-btn.data-v-91798fc9 {
  background-color: #f5f5f5;
  color: #666666;
}
.booking-modal .modal-actions .confirm-btn.data-v-91798fc9 {
  background-color: #ff6b35;
  color: #ffffff;
}

/* 加载状态样式 */
.loading-container.data-v-91798fc9 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  font-size: 32rpx;
  color: #666666;
}

/* 无时间段提示样式 */
.no-timeslots.data-v-91798fc9 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80rpx 40rpx;
}
.no-timeslots .no-timeslots-text.data-v-91798fc9 {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 20rpx;
}
.no-timeslots .no-timeslots-tip.data-v-91798fc9 {
  font-size: 28rpx;
  color: #999999;
}

/* 错误状态样式 */
.error-container.data-v-91798fc9 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.error-container .retry-btn.data-v-91798fc9 {
  margin-top: 20rpx;
  padding: 16rpx 32rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 预约类型按钮样式 */
.booking-type-buttons.data-v-91798fc9 {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
  justify-content: center;
}
.booking-type-buttons .booking-type-btn-wrapper.data-v-91798fc9 {
  position: relative;
  flex: 0 0 auto;
  max-width: 200rpx;
}
.booking-type-buttons .booking-type-btn-wrapper .booking-type-btn.data-v-91798fc9 {
  width: 160rpx;
  height: 80rpx;
  background-color: #f8f8f8;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.booking-type-buttons .booking-type-btn-wrapper .booking-type-btn .btn-text.data-v-91798fc9 {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}
.booking-type-buttons .booking-type-btn-wrapper .booking-type-btn.active.data-v-91798fc9 {
  background-color: #ff6b35;
  border-color: #ff6b35;
}
.booking-type-buttons .booking-type-btn-wrapper .booking-type-btn.active .btn-text.data-v-91798fc9 {
  color: #ffffff;
  font-weight: 600;
}
.booking-type-buttons .booking-type-btn-wrapper .booking-type-btn.data-v-91798fc9:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.booking-type-buttons .booking-type-btn-wrapper .help-icon.data-v-91798fc9 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
}
.booking-type-buttons .booking-type-btn-wrapper .help-icon .help-text.data-v-91798fc9 {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
}
.booking-type-buttons .booking-type-btn-wrapper .help-icon.data-v-91798fc9:active {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}

/* 帮助说明弹窗样式 */
.help-modal-overlay.data-v-91798fc9 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.help-modal-overlay .help-modal.data-v-91798fc9 {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 40rpx;
}
.help-modal-overlay .help-modal .help-header.data-v-91798fc9 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.help-modal-overlay .help-modal .help-header .help-title.data-v-91798fc9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.help-modal-overlay .help-modal .help-header .help-close.data-v-91798fc9 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.help-modal-overlay .help-modal .help-header .help-close .close-icon.data-v-91798fc9 {
  font-size: 32rpx;
  color: #666666;
  line-height: 1;
}
.help-modal-overlay .help-modal .help-header .help-close.data-v-91798fc9:active {
  background-color: #e0e0e0;
}
.help-modal-overlay .help-modal .help-body.data-v-91798fc9 {
  padding: 30rpx;
}
.help-modal-overlay .help-modal .help-body .help-description.data-v-91798fc9 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.help-modal-overlay .help-modal .help-footer.data-v-91798fc9 {
  padding: 20rpx 30rpx 30rpx;
}
.help-modal-overlay .help-modal .help-footer .help-confirm-btn.data-v-91798fc9 {
  width: 100%;
  height: 80rpx;
  background-color: #ff6b35;
  border: none;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-modal-overlay .help-modal .help-footer .help-confirm-btn .confirm-text.data-v-91798fc9 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}
.help-modal-overlay .help-modal .help-footer .help-confirm-btn.data-v-91798fc9:active {
  background-color: #e55a2b;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}