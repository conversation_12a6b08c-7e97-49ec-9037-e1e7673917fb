<view class="container data-v-1df554a0"><view class="navbar data-v-1df554a0"><view class="nav-left data-v-1df554a0" bindtap="{{a}}"><text class="nav-icon data-v-1df554a0">‹</text></view><text class="nav-title data-v-1df554a0">管理拼场</text><view class="nav-right data-v-1df554a0"></view></view><view wx:if="{{b}}" class="loading-state data-v-1df554a0"><text class="data-v-1df554a0">加载中...</text></view><view wx:elif="{{c}}" class="error-state data-v-1df554a0"><text class="error-icon data-v-1df554a0">⚠️</text><text class="error-text data-v-1df554a0">{{d}}</text><button class="retry-btn data-v-1df554a0" bindtap="{{e}}"> 重新加载 </button></view><view wx:elif="{{f}}" class="content data-v-1df554a0"><view class="info-section data-v-1df554a0"><view class="venue-header data-v-1df554a0"><text class="venue-name data-v-1df554a0">{{g}}</text><view class="{{['status-badge', 'data-v-1df554a0', i]}}"><text class="status-text data-v-1df554a0">{{h}}</text></view></view><view class="team-info data-v-1df554a0"><view class="team-header data-v-1df554a0"><text class="team-name data-v-1df554a0">{{j}}</text><text class="creator-label data-v-1df554a0">队长</text></view><view class="participants-progress data-v-1df554a0"><view class="progress-info data-v-1df554a0"><text class="progress-text data-v-1df554a0"> 参与人数：{{k}}/{{l}}人 </text><text class="progress-percent data-v-1df554a0">{{m}}% </text></view><view class="progress-bar data-v-1df554a0"><view class="progress-fill data-v-1df554a0" style="{{'width:' + n}}"></view></view></view></view><view class="activity-info data-v-1df554a0"><view class="info-row data-v-1df554a0"><text class="info-label data-v-1df554a0">活动时间</text><text class="info-value data-v-1df554a0">{{o}}</text></view><view class="info-row data-v-1df554a0"><text class="info-label data-v-1df554a0">每队费用</text><text class="info-value price data-v-1df554a0">¥{{p}}</text></view><view class="info-row data-v-1df554a0"><text class="info-label data-v-1df554a0">订单号</text><text class="info-value order-no data-v-1df554a0">{{q}}</text></view><view class="info-row data-v-1df554a0"><text class="info-label data-v-1df554a0">创建时间</text><text class="info-value data-v-1df554a0">{{r}}</text></view></view><view wx:if="{{s}}" class="description data-v-1df554a0"><text class="description-label data-v-1df554a0">活动描述</text><text class="description-text data-v-1df554a0">{{t}}</text></view></view><view class="participants-section data-v-1df554a0"><view class="section-title data-v-1df554a0"><text class="title-text data-v-1df554a0">队伍管理</text><text class="count-text data-v-1df554a0">({{v}}支)</text></view><view class="participants-list data-v-1df554a0"><view wx:for="{{w}}" wx:for-item="participant" wx:key="f" class="participant-item data-v-1df554a0"><view class="participant-info data-v-1df554a0"><image class="participant-avatar data-v-1df554a0" src="{{participant.a}}" mode="aspectFill"/><view class="participant-details data-v-1df554a0"><text class="participant-name data-v-1df554a0">{{participant.b}}</text><text class="participant-role data-v-1df554a0">{{participant.c}}</text></view></view><view wx:if="{{participant.d}}" class="remove-btn data-v-1df554a0" bindtap="{{participant.e}}"><text class="remove-text data-v-1df554a0">移除</text></view></view></view><view wx:if="{{x}}" class="empty-participants data-v-1df554a0"><text class="empty-icon data-v-1df554a0">👥</text><text class="empty-text data-v-1df554a0">暂无参与者</text></view></view><view class="settings-section data-v-1df554a0"><view class="section-title data-v-1df554a0"><text class="title-text data-v-1df554a0">拼场设置</text></view><view class="settings-list data-v-1df554a0"><view class="setting-item data-v-1df554a0"><view class="setting-info data-v-1df554a0"><text class="setting-label data-v-1df554a0">自动通过申请</text><text class="setting-desc data-v-1df554a0">{{y}}</text></view><switch class="data-v-1df554a0" checked="{{z}}" bindchange="{{A}}" disabled="{{B}}" color="#ff6b35"/></view><view class="setting-item data-v-1df554a0"><view class="setting-info data-v-1df554a0"><text class="setting-label data-v-1df554a0">允许中途退出</text><text class="setting-desc data-v-1df554a0">开启后，参与者可以在活动开始前退出</text></view><switch class="data-v-1df554a0" checked="{{C}}" bindchange="{{D}}" disabled="{{E}}" color="#ff6b35"/></view></view></view><view wx:if="{{F}}" class="requests-section data-v-1df554a0"><view class="section-title data-v-1df554a0"><text class="title-text data-v-1df554a0">拼场申请</text><text class="count-text data-v-1df554a0">({{G}}条待处理)</text></view><view class="requests-list data-v-1df554a0"><view wx:for="{{H}}" wx:for-item="request" wx:key="i" class="request-item data-v-1df554a0"><view class="request-info data-v-1df554a0"><image class="request-avatar data-v-1df554a0" src="{{request.a}}" mode="aspectFill"/><view class="request-details data-v-1df554a0"><text class="request-name data-v-1df554a0">{{request.b}}</text><text class="request-time data-v-1df554a0">{{request.c}}</text></view></view><view class="request-actions data-v-1df554a0"><view wx:if="{{request.d}}" class="action-buttons data-v-1df554a0"><button class="action-btn reject-btn data-v-1df554a0" bindtap="{{request.e}}"> 拒绝 </button><button class="action-btn approve-btn data-v-1df554a0" bindtap="{{request.f}}"> 同意 </button></view><view wx:else class="request-status data-v-1df554a0"><text class="{{['status-text', 'data-v-1df554a0', request.h]}}">{{request.g}}</text></view></view></view></view></view></view><view wx:if="{{I}}" class="bottom-actions data-v-1df554a0"><button wx:if="{{J}}" class="action-btn confirm-btn data-v-1df554a0" bindtap="{{K}}"> 确认拼场 </button><button wx:if="{{L}}" class="action-btn cancel-btn data-v-1df554a0" bindtap="{{M}}"> 取消拼场 </button></view><uni-popup wx:if="{{R}}" class="r data-v-1df554a0" u-s="{{['d']}}" u-r="removePopup" u-i="1df554a0-0" bind:__l="__l" u-p="{{R}}"><uni-popup-dialog wx:if="{{P}}" class="data-v-1df554a0" bindconfirm="{{N}}" bindclose="{{O}}" u-i="1df554a0-1,1df554a0-0" bind:__l="__l" u-p="{{P}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{W}}" class="r data-v-1df554a0" u-s="{{['d']}}" u-r="cancelPopup" u-i="1df554a0-2" bind:__l="__l" u-p="{{W}}"><uni-popup-dialog wx:if="{{U}}" class="data-v-1df554a0" bindconfirm="{{S}}" bindclose="{{T}}" u-i="1df554a0-3,1df554a0-2" bind:__l="__l" u-p="{{U}}"></uni-popup-dialog></uni-popup></view>