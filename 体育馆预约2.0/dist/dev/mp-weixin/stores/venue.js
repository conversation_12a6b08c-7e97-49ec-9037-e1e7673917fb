"use strict";
const common_vendor = require("../common/vendor.js");
const api_venue = require("../api/venue.js");
const api_timeslot = require("../api/timeslot.js");
const utils_toast = require("../utils/toast.js");
const utils_timeslotConstants = require("../utils/timeslot-constants.js");
const useVenueStore = common_vendor.defineStore("venue", {
  state: () => ({
    venueList: [],
    popularVenues: [],
    venueDetail: null,
    venueTypes: [],
    timeSlots: [],
    selectedTimeSlots: [],
    searchResults: [],
    loading: false,
    error: null,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 1
    },
    currentVenueId: null,
    currentDate: null,
    // 简化的缓存机制
    cache: {
      timeSlots: /* @__PURE__ */ new Map(),
      // key: venueId_date, value: { data, timestamp }
      venues: /* @__PURE__ */ new Map(),
      details: /* @__PURE__ */ new Map()
    },
    cacheTimeout: 5 * 60 * 1e3
    // 5分钟TTL
  }),
  getters: {
    // 场馆列表相关 - 这些应该是getter，返回状态值
    venueListGetter: (state) => state.venueList,
    popularVenuesGetter: (state) => state.popularVenues,
    venueDetailGetter: (state) => state.venueDetail,
    venueTypesGetter: (state) => state.venueTypes,
    timeSlotsGetter: (state) => state.timeSlots,
    searchResultsGetter: (state) => state.searchResults,
    // 状态相关
    isLoading: (state) => state.loading,
    getPagination: (state) => state.pagination,
    // 计算属性
    totalVenues: (state) => state.venueList.length,
    hasMoreVenues: (state) => state.pagination.current < state.pagination.totalPages,
    // 按类型筛选场馆
    getVenuesByType: (state) => (typeId) => {
      if (!typeId)
        return state.venueList;
      return state.venueList.filter((venue) => venue.typeId === typeId);
    },
    // 获取可用时间段
    getAvailableTimeSlots: (state) => {
      return state.timeSlots.filter((slot) => slot.status === utils_timeslotConstants.SLOT_STATUS.AVAILABLE);
    },
    // 获取已占用时间段
    getOccupiedTimeSlots: (state) => {
      return state.timeSlots.filter((slot) => slot.status === utils_timeslotConstants.SLOT_STATUS.OCCUPIED);
    },
    // 获取过期时间段
    getExpiredTimeSlots: (state) => {
      return state.timeSlots.filter((slot) => slot.status === utils_timeslotConstants.SLOT_STATUS.EXPIRED);
    }
  },
  actions: {
    // 缓存辅助方法
    isCacheValid(timestamp) {
      return Date.now() - timestamp < this.cacheTimeout;
    },
    getCachedData(cacheMap, key) {
      const cached = cacheMap.get(key);
      if (cached && this.isCacheValid(cached.timestamp)) {
        console.log("[VenueStore] 使用缓存数据:", key);
        return cached.data;
      }
      return null;
    },
    setCachedData(cacheMap, key, data) {
      cacheMap.set(key, {
        data,
        timestamp: Date.now()
      });
      console.log("[VenueStore] 缓存数据:", key);
    },
    clearExpiredCache() {
      const now = Date.now();
      for (const [key, value] of this.cache.timeSlots.entries()) {
        if (now - value.timestamp >= this.cacheTimeout) {
          this.cache.timeSlots.delete(key);
        }
      }
      for (const [key, value] of this.cache.venues.entries()) {
        if (now - value.timestamp >= this.cacheTimeout) {
          this.cache.venues.delete(key);
        }
      }
      for (const [key, value] of this.cache.details.entries()) {
        if (now - value.timestamp >= this.cacheTimeout) {
          this.cache.details.delete(key);
        }
      }
    },
    // 🔥 修复：增强的订单过期监听器设置
    setupOrderExpiredListener() {
      console.log("[VenueStore] 🔧 开始设置订单过期监听器");
      if (typeof common_vendor.index === "undefined") {
        console.warn("[VenueStore] ⚠️ uni对象不存在，延迟设置监听器");
        setTimeout(() => this.setupOrderExpiredListener(), 1e3);
        return;
      }
      if (!common_vendor.index.$on || !common_vendor.index.$off) {
        console.warn("[VenueStore] ⚠️ uni事件方法不存在，延迟设置监听器");
        setTimeout(() => this.setupOrderExpiredListener(), 1e3);
        return;
      }
      try {
        common_vendor.index.$off("order-expired", this.onOrderExpired);
        console.log("[VenueStore] 🧹 已清除旧的订单过期监听器");
        const boundHandler = this.onOrderExpired.bind(this);
        common_vendor.index.$on("order-expired", boundHandler);
        console.log("[VenueStore] ✅ 订单过期事件监听器已设置");
        this._orderExpiredHandler = boundHandler;
        this.setupAdditionalEventListeners();
      } catch (error) {
        console.error("[VenueStore] ❌ 设置订单过期监听器失败:", error);
        setTimeout(() => this.setupOrderExpiredListener(), 2e3);
      }
    },
    // 🔥 新增：设置其他相关事件监听器
    setupAdditionalEventListeners() {
      try {
        common_vendor.index.$off("booking-success", this.onBookingSuccess);
        common_vendor.index.$on("booking-success", this.onBookingSuccess.bind(this));
        console.log("[VenueStore] ✅ 其他事件监听器设置完成");
      } catch (error) {
        console.error("[VenueStore] ❌ 设置其他事件监听器失败:", error);
      }
    },
    // 🔥 新增：清理事件监听器
    cleanupEventListeners() {
      try {
        if (typeof common_vendor.index !== "undefined" && common_vendor.index.$off) {
          if (this._orderExpiredHandler) {
            common_vendor.index.$off("order-expired", this._orderExpiredHandler);
          }
          common_vendor.index.$off("booking-success", this.onBookingSuccess);
          console.log("[VenueStore] 🧹 事件监听器清理完成");
        }
      } catch (error) {
        console.error("[VenueStore] ❌ 清理事件监听器失败:", error);
      }
    },
    // 设置加载状态
    setLoading(loading) {
      this.loading = loading;
    },
    // 设置场馆列表
    setVenueList({ list, pagination }) {
      this.venueList = list;
      if (pagination) {
        this.pagination = { ...this.pagination, ...pagination };
      }
    },
    // 追加场馆列表（分页加载）
    appendVenueList(list) {
      this.venueList = [...this.venueList, ...list];
    },
    // 设置热门场馆
    setPopularVenues(venues) {
      this.popularVenues = venues;
    },
    // 设置场馆详情
    setVenueDetail(venue) {
      this.venueDetail = venue;
    },
    // 设置场馆类型
    setVenueTypes(types) {
      this.venueTypes = types;
    },
    // 设置时间段
    setTimeSlots(slots) {
      console.log("[VenueStore] setTimeSlots 被调用，参数:", slots);
      console.log("[VenueStore] setTimeSlots 参数类型:", typeof slots);
      console.log("[VenueStore] setTimeSlots 是否为数组:", Array.isArray(slots));
      if (Array.isArray(slots)) {
        this.timeSlots = slots;
      } else {
        console.warn("[VenueStore] setTimeSlots 收到非数组参数，强制设置为空数组");
        this.timeSlots = [];
      }
      console.log("[VenueStore] setTimeSlots 设置后的值:", this.timeSlots);
    },
    // 设置搜索结果
    setSearchResults(results) {
      this.searchResults = results;
    },
    // 设置分页信息
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
    // 获取场馆列表
    async getVenueList(params = {}) {
      try {
        console.log("[VenueStore] 开始获取场馆列表，参数:", params);
        this.setLoading(true);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("请求超时")), 1e4);
        });
        const apiPromise = api_venue.venueApi.getVenueList(params);
        const response = await Promise.race([apiPromise, timeoutPromise]);
        console.log("[VenueStore] 场馆API响应:", response);
        let list = [];
        let pagination = {
          current: 1,
          pageSize: 10,
          total: 0,
          totalPages: 1
        };
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            list = response.data;
            pagination = {
              current: response.page || params.page || 1,
              pageSize: response.pageSize || params.pageSize || 10,
              total: response.total || response.data.length,
              totalPages: response.totalPages || 1
            };
          } else {
            console.warn("[VenueStore] API响应数据格式异常，使用空数组:", response);
          }
        } else if (response && Array.isArray(response)) {
          list = response;
          pagination.total = response.length;
        } else {
          console.warn("[VenueStore] API响应为空或格式错误，使用空数组:", response);
        }
        console.log("[VenueStore] 解析的场馆列表:", list);
        console.log("[VenueStore] 分页信息:", pagination);
        if (params.page === 1 || params.refresh) {
          this.setVenueList({ list, pagination });
        } else {
          this.appendVenueList(list);
          this.setVenueList({ list: this.venueList, pagination });
        }
        return response;
      } catch (error) {
        console.error("[VenueStore] 获取场馆列表失败:", error);
        utils_toast.showError(error.message || "获取场馆列表失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取热门场馆
    async getPopularVenues() {
      try {
        console.log("[VenueStore] 开始获取热门场馆");
        const response = await api_venue.venueApi.getPopularVenues();
        console.log("[VenueStore] 热门场馆API响应:", response);
        let venues = [];
        if (Array.isArray(response)) {
          venues = response;
        } else if (response && Array.isArray(response.data)) {
          venues = response.data;
        } else {
          console.warn("[VenueStore] 热门场馆API响应格式异常:", response);
        }
        this.setPopularVenues(venues);
        console.log("[VenueStore] 热门场馆数据已设置:", venues);
        return response;
      } catch (error) {
        console.error("[VenueStore] 获取热门场馆失败:", error);
        this.setPopularVenues([]);
        utils_toast.showError(error.message || "获取热门场馆失败");
        throw error;
      }
    },
    // 获取场馆详情
    async getVenueDetail(venueId) {
      try {
        console.log("[VenueStore] 开始获取场馆详情:", venueId);
        this.setLoading(true);
        const response = await api_venue.venueApi.getVenueDetail(venueId);
        console.log("[VenueStore] 完整API响应:", response);
        console.log("[VenueStore] 响应数据类型:", typeof response);
        console.log("[VenueStore] 响应数据结构:", Object.keys(response || {}));
        if (response && response.data) {
          this.setVenueDetail(response.data);
          console.log("[VenueStore] 场馆详情获取成功:", response.data);
        } else if (response) {
          this.setVenueDetail(response);
          console.log("[VenueStore] 场馆详情获取成功（直接响应）:", response);
        }
        return response;
      } catch (error) {
        console.error("[VenueStore] 获取场馆详情失败:", error);
        utils_toast.showError(error.message || "获取场馆详情失败");
        throw error;
      } finally {
        this.setLoading(false);
      }
    },
    // 获取场馆类型
    async getVenueTypes() {
      try {
        console.log("[VenueStore] 开始获取场馆类型");
        const response = await api_venue.venueApi.getVenueTypes();
        if (response && response.data) {
          this.setVenueTypes(response.data);
          console.log("[VenueStore] 场馆类型获取成功:", response.data);
        }
        return response;
      } catch (error) {
        console.error("[VenueStore] 获取场馆类型失败:", error);
        utils_toast.showError(error.message || "获取场馆类型失败");
        throw error;
      }
    },
    // 获取时间段 - 简化版
    async getTimeSlots(venueId, date, forceRefresh = false, loading = true) {
      try {
        console.log("[VenueStore] 获取时间段:", { venueId, date, forceRefresh, loading });
        if (loading) {
          this.setLoading(true);
        }
        const cacheKey = `${venueId}_${date}`;
        if (!forceRefresh) {
          const cachedData = this.getCachedData(this.cache.timeSlots, cacheKey);
          if (cachedData) {
            this.setTimeSlots(cachedData);
            this.currentVenueId = venueId;
            this.currentDate = date;
            return { data: cachedData, success: true };
          }
        }
        console.log("[VenueStore] 调用API参数详情:", {
          venueId,
          venueIdType: typeof venueId,
          date,
          dateType: typeof date,
          forceRefresh
        });
        const response = await api_timeslot.timeslotApi.getVenueTimeSlots(venueId, date, { forceRefresh });
        let timeSlots = [];
        if (response && response.success && Array.isArray(response.data)) {
          timeSlots = response.data;
        } else if (response && Array.isArray(response)) {
          timeSlots = response;
        }
        this.setCachedData(this.cache.timeSlots, cacheKey, timeSlots);
        this.setTimeSlots(timeSlots);
        this.currentVenueId = venueId;
        this.currentDate = date;
        console.log("[VenueStore] 时间段获取成功:", timeSlots.length, "个时间段");
        return { data: timeSlots, success: true };
      } catch (error) {
        console.error("[VenueStore] 获取时间段失败:", error);
        this.setTimeSlots([]);
        throw error;
      } finally {
        if (loading) {
          this.setLoading(false);
        }
      }
    },
    // 生成默认时间段（根据场馆营业时间，半小时间隔）
    generateDefaultTimeSlots(venueId, date) {
      console.log("[VenueStore] 开始生成默认时间段（根据营业时间，半小时间隔）");
      const venue = this.venueDetail;
      if (!venue) {
        console.error("[VenueStore] 场馆详情不存在，无法生成时间段");
        return;
      }
      const openTime = this.parseTimeString(venue.openTime || venue.open_time || "09:00");
      const closeTime = this.parseTimeString(venue.closeTime || venue.close_time || "22:00");
      const venueHourPrice = venue.price || 100;
      const venueHalfHourPrice = Math.round(venueHourPrice / 2);
      console.log("[VenueStore] 场馆营业时间:", `${openTime} - ${closeTime}`);
      console.log("[VenueStore] 场馆小时价格:", venueHourPrice);
      console.log("[VenueStore] 场馆半小时价格:", venueHalfHourPrice);
      const defaultSlots = [];
      const [startHour, startMinute] = openTime.split(":").map(Number);
      const [endHour, endMinute] = closeTime.split(":").map(Number);
      let currentHour = startHour;
      let currentMinute = startMinute;
      if (currentMinute > 0 && currentMinute < 30) {
        currentMinute = 30;
      } else if (currentMinute > 30) {
        currentHour += 1;
        currentMinute = 0;
      }
      while (currentHour < endHour || currentHour === endHour && currentMinute < endMinute) {
        const startTime = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`;
        let nextMinute = currentMinute + 30;
        let nextHour = currentHour;
        if (nextMinute >= 60) {
          nextHour += 1;
          nextMinute = 0;
        }
        const endTime = `${nextHour.toString().padStart(2, "0")}:${nextMinute.toString().padStart(2, "0")}`;
        if (nextHour > endHour || nextHour === endHour && nextMinute > endMinute) {
          break;
        }
        defaultSlots.push({
          id: `default_${venueId}_${date}_${currentHour}_${currentMinute}`,
          venueId: parseInt(venueId),
          date,
          startTime,
          endTime,
          price: venueHalfHourPrice,
          status: "AVAILABLE",
          isGenerated: true
          // 标记为前端生成
        });
        currentMinute = nextMinute;
        currentHour = nextHour;
      }
      console.log("[VenueStore] 生成的默认时间段数量:", defaultSlots.length);
      console.log("[VenueStore] 营业时间范围:", `${openTime} - ${closeTime}`);
      console.log("[VenueStore] 生成的时间段范围:", defaultSlots.length > 0 ? `${defaultSlots[0].startTime} - ${defaultSlots[defaultSlots.length - 1].endTime}` : "无");
      this.setTimeSlots(defaultSlots);
      console.log("[VenueStore] 默认时间段设置完成");
    },
    // 解析时间字符串，支持多种格式
    parseTimeString(timeStr) {
      if (!timeStr)
        return "09:00";
      if (timeStr.length > 5) {
        timeStr = timeStr.substring(0, 5);
      }
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
      if (!timeRegex.test(timeStr)) {
        console.warn("[VenueStore] 时间格式不正确:", timeStr, "使用默认值");
        return timeStr.includes("close") || timeStr.includes("end") ? "22:00" : "09:00";
      }
      return timeStr;
    },
    // 获取场馆时间段（别名方法，用于兼容性）
    async getVenueTimeSlots(venueId, date, forceRefresh = false, loading = true) {
      console.log("[VenueStore] getVenueTimeSlots 调用参数:", { venueId, date, forceRefresh, loading });
      if (typeof venueId === "object" && venueId.venueId && venueId.date) {
        const params = venueId;
        return await this.getTimeSlots(params.venueId, params.date, params.forceRefresh || false, params.loading !== false);
      }
      if (venueId && date) {
        return await this.getTimeSlots(venueId, date, forceRefresh, loading);
      }
      console.warn("[VenueStore] getVenueTimeSlots 参数格式错误:", { venueId, date });
      return { data: [] };
    },
    // 清理时间段缓存
    async clearTimeSlotCache(venueId, date) {
      try {
        console.log("[VenueStore] 开始清理时间段缓存:", { venueId, date });
        this.timeSlots = [];
        this.loading = false;
        this.currentVenueId = null;
        this.currentDate = null;
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
        console.log("[VenueStore] 时间段缓存清理完成");
      } catch (error) {
        console.warn("[VenueStore] 清理时间段缓存失败:", error);
      }
    },
    // 刷新时间段状态 - 简化版
    async refreshTimeSlotStatus(venueId, date, timeSlotId = null) {
      try {
        console.log("[VenueStore] 开始刷新时间段状态:", { venueId, date, timeSlotId });
        const result = await this.getTimeSlots(venueId, date, true, false);
        this.notifyTimeSlotUpdate(venueId, date, timeSlotId, result.data);
        console.log("[VenueStore] 时间段状态刷新完成:", result.data.length, "个时间段");
        return result.data;
      } catch (error) {
        console.error("[VenueStore] 刷新时间段状态失败:", error);
        this.notifyTimeSlotError(venueId, date, timeSlotId, error);
        this.setTimeSlots([]);
        return [];
      }
    },
    // 通知时间段更新
    notifyTimeSlotUpdate(venueId, date, timeSlotId = null, timeSlots = null) {
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.$emit) {
        const eventData = {
          venueId,
          date,
          timeSlots: timeSlots || this.timeSlots,
          updatedTimeSlotId: timeSlotId,
          timestamp: Date.now()
        };
        common_vendor.index.$emit("timeSlotUpdated", eventData);
        if (timeSlotId) {
          const updatedSlot = (timeSlots || this.timeSlots).find((slot) => slot.id === timeSlotId);
          common_vendor.index.$emit("timeSlotStatusChanged", {
            venueId,
            date,
            timeSlotId,
            newStatus: updatedSlot == null ? void 0 : updatedSlot.status,
            slot: updatedSlot,
            timestamp: Date.now()
          });
        }
        console.log("[VenueStore] 发送时间段更新事件");
      }
    },
    // 通知时间段错误事件
    notifyTimeSlotError(venueId, date, timeSlotId = null, error) {
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.$emit) {
        common_vendor.index.$emit("timeSlotError", {
          venueId,
          date,
          timeSlotId,
          error: error.message || error,
          timestamp: Date.now()
        });
        console.log("[VenueStore] 发送时间段错误事件");
      }
    },
    // 预约成功后的状态同步 - 简化版
    async onBookingSuccess(bookingData) {
      console.log("[VenueStore] 预约成功，开始状态同步:", bookingData);
      try {
        if (!bookingData) {
          console.warn("[VenueStore] 预约数据为空，跳过更新");
          return;
        }
        const venueId = bookingData.venueId || bookingData.venue_id;
        const date = bookingData.date || bookingData.booking_date;
        if (!venueId || !date) {
          console.warn("[VenueStore] 缺少必要字段，跳过更新");
          return;
        }
        const timeSlotId = bookingData.timeSlotId || bookingData.time_slot_id || bookingData.slotId;
        if (timeSlotId && this.timeSlots.length > 0) {
          const slot = this.timeSlots.find(
            (s) => s.id === timeSlotId || s.timeSlotId === timeSlotId
          );
          if (slot) {
            slot.status = utils_timeslotConstants.SLOT_STATUS.BOOKED;
            slot.isBooked = true;
            slot.isAvailable = false;
            slot.lastUpdated = (/* @__PURE__ */ new Date()).toISOString();
            this.setTimeSlots([...this.timeSlots]);
            console.log("[VenueStore] 本地状态更新成功，状态设置为BOOKED");
          }
        }
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
        setTimeout(async () => {
          try {
            await this.refreshTimeSlotStatus(venueId, date);
            if (typeof common_vendor.index !== "undefined" && common_vendor.index.$emit) {
              common_vendor.index.$emit("timeslot-updated", {
                venueId,
                date,
                action: "booking-success",
                timestamp: (/* @__PURE__ */ new Date()).toISOString()
              });
            }
            console.log("[VenueStore] 预约成功后状态刷新完成");
          } catch (error) {
            console.error("[VenueStore] 延迟刷新失败:", error);
          }
        }, 500);
      } catch (error) {
        console.error("[VenueStore] 预约成功状态同步失败:", error);
      }
    },
    // 取消预约后刷新时间段
    async onBookingCancelled(venueId, date, cancelledSlotIds) {
      try {
        console.log("[VenueStore] 预约取消，刷新时间段状态");
        if (Array.isArray(cancelledSlotIds) && cancelledSlotIds.length > 0) {
          const updatedSlots = this.timeSlots.map((slot) => {
            if (cancelledSlotIds.includes(slot.id)) {
              return {
                ...slot,
                status: utils_timeslotConstants.SLOT_STATUS.AVAILABLE,
                isBooked: false,
                isAvailable: true,
                lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
              };
            }
            return slot;
          });
          this.setTimeSlots(updatedSlots);
        }
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
        setTimeout(() => {
          this.getTimeSlots(venueId, date, true, false);
        }, 1e3);
      } catch (error) {
        console.error("[VenueStore] 预约取消后刷新失败:", error);
      }
    },
    // 搜索场馆
    async searchVenues(searchParams) {
      try {
        this.setLoading(true);
        console.log("[VenueStore] 开始搜索场馆:", searchParams);
        let params = {};
        if (typeof searchParams === "string") {
          params.keyword = searchParams;
        } else if (typeof searchParams === "object") {
          params = { ...searchParams };
        }
        const response = await api_venue.venueApi.searchVenues(params);
        console.log("[VenueStore] 搜索响应:", response);
        if (response && response.data) {
          this.setSearchResults(response.data);
          console.log("[VenueStore] 搜索结果已设置:", response.data.length, "个场馆");
          return response.data;
        }
        console.warn("[VenueStore] 搜索响应无数据");
        this.setSearchResults([]);
        return [];
      } catch (error) {
        console.error("[VenueStore] 搜索场馆失败:", error);
        this.setSearchResults([]);
        return [];
      } finally {
        this.setLoading(false);
      }
    },
    // 清空场馆详情
    clearVenueDetail() {
      this.venueDetail = null;
    },
    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = [];
    },
    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      };
    },
    // 清除时间段缓存
    clearTimeSlots() {
      console.log("[VenueStore] 清除时间段缓存");
      this.timeSlots = [];
      this.selectedTimeSlots = [];
      this.currentVenueId = null;
      this.currentDate = null;
      this.cache.timeSlots.clear();
    },
    // 强力刷新时间段
    async forceRefreshTimeSlots(venueId, date) {
      try {
        console.log("[VenueStore] 强力刷新时间段:", { venueId, date });
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
        return await this.getTimeSlots(venueId, date, true, false);
      } catch (error) {
        console.error("[VenueStore] 强力刷新时间段失败:", error);
        throw error;
      }
    },
    // 立即释放时间段（用于取消预约后的即时更新）
    immediateReleaseTimeSlots(venueId, date, startTime, endTime) {
      try {
        console.log("[VenueStore] 立即释放时间段:", { venueId, date, startTime, endTime });
        if (this.timeSlots && Array.isArray(this.timeSlots)) {
          let releasedCount = 0;
          const updatedSlots = this.timeSlots.map((slot) => {
            const slotStart = slot.startTime;
            const slotEnd = slot.endTime;
            const isInRange = slotStart >= startTime && slotEnd <= endTime || slotStart === startTime && slotEnd === endTime || slotStart >= startTime && slotStart < endTime || slotEnd > startTime && slotEnd <= endTime;
            if (isInRange && (slot.status === "BOOKED" || slot.status === "RESERVED" || slot.status === "SHARING")) {
              console.log("[VenueStore] 立即释放时间段:", slot.id, slot.startTime, "-", slot.endTime, "状态:", slot.status);
              releasedCount++;
              return {
                ...slot,
                status: "AVAILABLE",
                isBooked: false,
                isAvailable: true,
                lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
              };
            }
            return slot;
          });
          this.setTimeSlots(updatedSlots);
          console.log(`[VenueStore] 时间段状态已立即更新，释放了 ${releasedCount} 个时间段`);
        }
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
      } catch (error) {
        console.error("[VenueStore] 立即释放时间段失败:", error);
      }
    },
    // 🔧 新增：强制释放拼场时间段（专门用于拼场订单取消）
    forceReleaseSharingTimeSlots(venueId, date, startTime, endTime) {
      try {
        console.log("[VenueStore] 🎯 强制释放拼场时间段:", { venueId, date, startTime, endTime });
        if (this.timeSlots && Array.isArray(this.timeSlots)) {
          let releasedCount = 0;
          const updatedSlots = this.timeSlots.map((slot) => {
            const slotStart = slot.startTime;
            const slotEnd = slot.endTime;
            const shouldRelease = (
              // 精确匹配
              slotStart === startTime && slotEnd === endTime || // 时间范围重叠
              slotStart >= startTime && slotStart < endTime || slotEnd > startTime && slotEnd <= endTime || // 包含关系
              slotStart <= startTime && slotEnd >= endTime || slotStart >= startTime && slotEnd <= endTime
            );
            if (shouldRelease && slot.status !== "AVAILABLE") {
              console.log("[VenueStore] 🎯 强制释放拼场时间段:", slot.id, slot.startTime, "-", slot.endTime, "原状态:", slot.status);
              releasedCount++;
              return {
                ...slot,
                status: "AVAILABLE",
                isBooked: false,
                isAvailable: true,
                lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
              };
            }
            return slot;
          });
          this.setTimeSlots(updatedSlots);
          console.log(`[VenueStore] 🎯 拼场时间段强制释放完成，释放了 ${releasedCount} 个时间段`);
          this.$forceUpdate && this.$forceUpdate();
        }
        const cacheKey = `${venueId}_${date}`;
        this.cache.timeSlots.delete(cacheKey);
        this.cache.venues.clear();
      } catch (error) {
        console.error("[VenueStore] 强制释放拼场时间段失败:", error);
      }
    },
    // 订单过期事件处理
    onOrderExpired(orderData) {
      try {
        console.log("[VenueStore] 订单过期事件:", orderData);
        if (orderData.venueId && orderData.date) {
          const cacheKey = `${orderData.venueId}_${orderData.date}`;
          this.cache.timeSlots.delete(cacheKey);
          this.getTimeSlots(orderData.venueId, orderData.date, true, false);
        }
      } catch (error) {
        console.error("[VenueStore] 处理订单过期事件失败:", error);
      }
    },
    // 🔄 更新时间段状态（用于实时验证后的状态同步）
    updateTimeSlotsStatus(venueId, date, latestSlots) {
      try {
        console.log("[VenueStore] 🔄 更新时间段状态:", {
          venueId,
          date,
          slotsCount: (latestSlots == null ? void 0 : latestSlots.length) || 0
        });
        if (!venueId || !date || !Array.isArray(latestSlots)) {
          console.warn("[VenueStore] ⚠️ 更新时间段状态参数无效");
          return false;
        }
        if (this.currentVenueId === venueId && this.currentDate === date) {
          this.setTimeSlots(latestSlots);
          console.log("[VenueStore] ✅ 时间段状态已更新:", latestSlots.length, "个时间段");
          this.notifyTimeSlotUpdate(venueId, date, null, latestSlots);
          if (typeof common_vendor.index !== "undefined" && common_vendor.index.$emit) {
            common_vendor.index.$emit("timeslots-status-updated", {
              venueId,
              date,
              timeSlots: latestSlots,
              timestamp: Date.now(),
              source: "realtime-validation"
            });
          }
          return true;
        } else {
          console.log("[VenueStore] 📍 场馆或日期不匹配，跳过更新:", {
            current: { venueId: this.currentVenueId, date: this.currentDate },
            target: { venueId, date }
          });
          return false;
        }
      } catch (error) {
        console.error("[VenueStore] ❌ 更新时间段状态失败:", error);
        return false;
      }
    }
  }
});
exports.useVenueStore = useVenueStore;
