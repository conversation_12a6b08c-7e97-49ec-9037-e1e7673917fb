<view class="container data-v-afb09895"><view class="status-filter data-v-afb09895"><view wx:for="{{a}}" wx:for-item="status" wx:key="b" class="{{['filter-item', 'data-v-afb09895', status.c && 'active']}}" bindtap="{{status.d}}">{{status.a}}</view></view><view class="booking-list data-v-afb09895"><view wx:for="{{b}}" wx:for-item="booking" wx:key="P" class="booking-card data-v-afb09895" bindtap="{{booking.Q}}"><view class="card-header data-v-afb09895"><view class="venue-info data-v-afb09895"><view class="venue-name-row data-v-afb09895"><text class="venue-name data-v-afb09895">{{booking.a}}</text><view wx:if="{{booking.b}}" class="booking-type-tag data-v-afb09895"><text class="{{['tag-text', 'data-v-afb09895', booking.d]}}">{{booking.c}}</text></view><view wx:if="{{booking.e}}" class="virtual-order-tag data-v-afb09895"><text class="virtual-tag-text data-v-afb09895">拼场申请</text></view></view><text class="booking-date data-v-afb09895">{{booking.f}}</text></view><view class="{{['booking-status', 'data-v-afb09895', booking.h]}}">{{booking.g}}</view></view><view class="card-content data-v-afb09895"><view class="time-info data-v-afb09895"><text class="time-icon data-v-afb09895">🕐</text><text class="time-text data-v-afb09895">{{booking.i}}</text></view><view class="location-info data-v-afb09895"><text class="location-icon data-v-afb09895">📍</text><text class="location-text data-v-afb09895">{{booking.j}}</text></view><view class="order-info data-v-afb09895"><text class="order-icon data-v-afb09895">📋</text><text class="order-text data-v-afb09895">订单号：{{booking.k}}</text></view><view class="create-time-info data-v-afb09895"><text class="time-icon data-v-afb09895">📅</text><text class="create-time-text data-v-afb09895">创建时间：{{booking.l}}</text></view><view class="price-info data-v-afb09895"><text class="price-label data-v-afb09895">费用：</text><text class="price-value data-v-afb09895">¥{{booking.m}}</text></view><countdown-timer wx:if="{{booking.n}}" class="simple data-v-afb09895" bindexpired="{{booking.o}}" u-i="{{booking.p}}" bind:__l="__l" u-p="{{booking.q}}"/></view><view class="card-actions data-v-afb09895"><block wx:if="{{booking.r}}"><button class="action-btn pay-btn data-v-afb09895" catchtap="{{booking.s}}">立即支付</button><button class="action-btn cancel-btn data-v-afb09895" catchtap="{{booking.t}}">取消预约</button></block><block wx:elif="{{booking.v}}"><button class="action-btn info-btn data-v-afb09895" catchtap="{{booking.w}}">查看详情</button><button class="action-btn cancel-btn data-v-afb09895" catchtap="{{booking.x}}">取消预约</button></block><block wx:elif="{{booking.y}}"><button class="action-btn info-btn data-v-afb09895" catchtap="{{booking.z}}">查看详情</button><button class="action-btn participants-btn data-v-afb09895" catchtap="{{booking.A}}">查看参与者</button><button class="action-btn cancel-btn data-v-afb09895" catchtap="{{booking.B}}">取消预约</button></block><block wx:elif="{{booking.C}}"><button class="action-btn info-btn data-v-afb09895" catchtap="{{booking.D}}">查看详情</button><button class="action-btn participants-btn data-v-afb09895" catchtap="{{booking.E}}">查看参与者</button></block><block wx:elif="{{booking.F}}"><button class="action-btn checkin-btn data-v-afb09895" catchtap="{{booking.G}}">签到</button><button class="action-btn cancel-btn data-v-afb09895" catchtap="{{booking.H}}">取消预约</button></block><block wx:elif="{{booking.I}}"><button class="action-btn complete-btn data-v-afb09895" catchtap="{{booking.J}}">完成订单</button></block><block wx:elif="{{booking.K}}"><button class="action-btn review-btn data-v-afb09895" catchtap="{{booking.L}}">评价场馆</button><button class="action-btn rebook-btn data-v-afb09895" catchtap="{{booking.M}}">再次预约</button></block><block wx:elif="{{booking.N}}"><button class="action-btn rebook-btn data-v-afb09895" catchtap="{{booking.O}}">再次预约</button></block></view></view></view><view wx:if="{{c}}" class="empty-state data-v-afb09895"><text class="empty-icon data-v-afb09895">📅</text><text class="empty-text data-v-afb09895">暂无预约记录</text><button class="empty-btn data-v-afb09895" bindtap="{{d}}">去预约场馆</button></view><view wx:if="{{e}}" class="load-more data-v-afb09895" bindtap="{{g}}"><text class="data-v-afb09895">{{f}}</text></view><uni-popup wx:if="{{l}}" class="r data-v-afb09895" u-s="{{['d']}}" u-r="cancelPopup" data-c-h="{{!k}}" u-i="afb09895-1" bind:__l="__l" u-p="{{l}}"><view class="cancel-modal data-v-afb09895"><view class="modal-header data-v-afb09895"><text class="modal-title data-v-afb09895">取消预约</text></view><view class="modal-content data-v-afb09895"><text class="modal-text data-v-afb09895">确定要取消这个预约吗？</text><text class="modal-note data-v-afb09895">取消后可能产生手续费，具体以场馆规定为准</text></view><view class="modal-actions data-v-afb09895"><button class="modal-btn cancel-btn data-v-afb09895" bindtap="{{h}}">暂不取消</button><button class="modal-btn confirm-btn data-v-afb09895" bindtap="{{i}}">确认取消</button></view></view></uni-popup></view>