<view class="container data-v-b0f57c8f"><view class="header data-v-b0f57c8f"><text class="title data-v-b0f57c8f">Order字段传递测试</text></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试参数</view><view class="test-item data-v-b0f57c8f"><text class="test-label data-v-b0f57c8f">场馆ID:</text><input placeholder="输入场馆ID" class="test-input data-v-b0f57c8f" value="{{a}}" bindinput="{{b}}"/></view><view class="test-item data-v-b0f57c8f"><text class="test-label data-v-b0f57c8f">测试日期:</text><input placeholder="YYYY-MM-DD" class="test-input data-v-b0f57c8f" value="{{c}}" bindinput="{{d}}"/></view><view class="test-item data-v-b0f57c8f"><text class="test-label data-v-b0f57c8f">开始时间:</text><input placeholder="HH:MM" class="test-input data-v-b0f57c8f" value="{{e}}" bindinput="{{f}}"/></view><view class="test-item data-v-b0f57c8f"><text class="test-label data-v-b0f57c8f">结束时间:</text><input placeholder="HH:MM" class="test-input data-v-b0f57c8f" value="{{g}}" bindinput="{{h}}"/></view><view class="test-item data-v-b0f57c8f"><text class="test-label data-v-b0f57c8f">每队价格:</text><input placeholder="输入价格" class="test-input data-v-b0f57c8f" type="number" value="{{i}}" bindinput="{{j}}"/></view></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试1: 拼场数据构建</view><button bindtap="{{k}}" class="test-button data-v-b0f57c8f">构建拼场数据</button><view wx:if="{{l}}" class="test-result data-v-b0f57c8f"><text class="result-title data-v-b0f57c8f">构建结果:</text><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">开始时间: {{m}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">结束时间: {{n}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">每队价格: ¥{{o}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">队伍名称: {{p}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">联系方式: {{q}}</text></view></view></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试2: 模拟拼场创建</view><button bindtap="{{r}}" class="test-button data-v-b0f57c8f">创建拼场订单</button><view wx:if="{{s}}" class="test-result data-v-b0f57c8f"><text class="result-title data-v-b0f57c8f">创建结果:</text><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">成功: {{t}}</text></view><view wx:if="{{v}}" class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">订单ID: {{w}}</text></view><view wx:if="{{x}}" class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">订单号: {{y}}</text></view><view wx:if="{{z}}" class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">错误: {{A}}</text></view></view></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试3: 数据传递验证</view><button bindtap="{{B}}" class="test-button data-v-b0f57c8f">验证数据传递</button><view wx:if="{{C}}" class="test-result data-v-b0f57c8f"><text class="result-title data-v-b0f57c8f">传递验证:</text><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">时间传递: {{D}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">价格传递: {{E}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">字段传递: {{F}}</text></view><view wx:if="{{G}}" class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">问题: {{H}}</text></view></view></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试4: Order字段分析</view><button bindtap="{{I}}" class="test-button data-v-b0f57c8f">分析Order字段</button><view wx:if="{{J}}" class="test-result data-v-b0f57c8f"><text class="result-title data-v-b0f57c8f">字段分析:</text><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">字段使用率: {{K}}%</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">已使用字段: {{L}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">未使用字段: {{M}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">时间字段: {{N}}</text></view><view class="result-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">价格字段: {{O}}</text></view></view></view><view class="test-section data-v-b0f57c8f"><view class="section-title data-v-b0f57c8f">测试日志</view><button bindtap="{{P}}" class="test-button secondary data-v-b0f57c8f">清除日志</button><view class="test-logs data-v-b0f57c8f"><view wx:for="{{Q}}" wx:for-item="log" wx:key="b" class="log-item data-v-b0f57c8f"><text class="data-v-b0f57c8f">{{log.a}}</text></view></view></view></view>