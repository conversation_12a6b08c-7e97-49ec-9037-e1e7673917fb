/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.countdown-container.data-v-357e1131 {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}
.countdown-container .countdown-icon.data-v-357e1131 {
  margin-right: 6rpx;
  font-size: 24rpx;
}
.countdown-container .countdown-content.data-v-357e1131 {
  display: flex;
  flex-direction: column;
}
.countdown-container .countdown-content .countdown-label.data-v-357e1131 {
  font-size: 18rpx;
  opacity: 0.8;
  margin-bottom: 2rpx;
}
.countdown-container .countdown-content .countdown-time.data-v-357e1131 {
  font-size: 20rpx;
  font-weight: bold;
}
.countdown-container.countdown-normal.data-v-357e1131 {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  color: #52c41a;
}
.countdown-container.countdown-normal .countdown-icon.data-v-357e1131 {
  color: #52c41a;
}
.countdown-container.countdown-warning.data-v-357e1131 {
  background-color: #fff7e6;
  border: 1rpx solid #ffd591;
  color: #fa8c16;
}
.countdown-container.countdown-warning .countdown-icon.data-v-357e1131 {
  color: #fa8c16;
}
.countdown-container.countdown-urgent.data-v-357e1131 {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  color: #ff4d4f;
  -webkit-animation: blink-357e1131 1s infinite;
          animation: blink-357e1131 1s infinite;
}
.countdown-container.countdown-urgent .countdown-icon.data-v-357e1131 {
  color: #ff4d4f;
}
.countdown-container.countdown-expired.data-v-357e1131 {
  background-color: #f5f5f5;
  border: 1rpx solid #d9d9d9;
  color: #999999;
}
.countdown-container.countdown-expired .countdown-icon.data-v-357e1131 {
  color: #999999;
}
@-webkit-keyframes blink-357e1131 {
0%, 50% {
    opacity: 1;
}
51%, 100% {
    opacity: 0.5;
}
}
@keyframes blink-357e1131 {
0%, 50% {
    opacity: 1;
}
51%, 100% {
    opacity: 0.5;
}
}
.countdown-container.simple.data-v-357e1131 {
  padding: 4rpx 8rpx;
  font-size: 18rpx;
}
.countdown-container.simple .countdown-icon.data-v-357e1131 {
  font-size: 20rpx;
  margin-right: 4rpx;
}
.countdown-container.simple .countdown-content.data-v-357e1131 {
  flex-direction: row;
  align-items: center;
}
.countdown-container.simple .countdown-content .countdown-label.data-v-357e1131 {
  margin-right: 4rpx;
  margin-bottom: 0;
}
.countdown-container.simple .countdown-content .countdown-time.data-v-357e1131 {
  font-size: 18rpx;
}