<view class="container data-v-83a5a03c"><view class="dev-toolbar data-v-83a5a03c"><text class="dev-title data-v-83a5a03c">🔧 开发工具</text><view class="dev-actions data-v-83a5a03c"><button class="dev-btn data-v-83a5a03c" bindtap="{{a}}"> 🔍 最终验证 </button><button class="dev-btn data-v-83a5a03c" bindtap="{{b}}"> 🧪 迁移验证 </button><button class="dev-btn data-v-83a5a03c" bindtap="{{c}}"> 🔐 登录页面 </button><button class="dev-btn data-v-83a5a03c" bindtap="{{d}}"> 📋 我的预订 </button><button class="dev-btn data-v-83a5a03c" bindtap="{{e}}"> 🤝 拼场列表 </button></view></view><skeleton-screen wx:if="{{f}}" class="data-v-83a5a03c" u-i="83a5a03c-0" bind:__l="__l" u-p="{{g}}"/><view wx:else class="data-v-83a5a03c"><view class="banner-section data-v-83a5a03c"><swiper class="banner-swiper data-v-83a5a03c" indicator-dots circular autoplay><swiper-item wx:for="{{h}}" wx:for-item="banner" wx:key="d" class="data-v-83a5a03c"><image src="{{banner.a}}" class="banner-image data-v-83a5a03c" mode="aspectFill" lazy-load bindload="{{banner.b}}" binderror="{{banner.c}}"/></swiper-item></swiper></view><view class="quick-actions data-v-83a5a03c"><view class="action-item data-v-83a5a03c" bindtap="{{i}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-venue data-v-83a5a03c">🏟️</text></view><text class="action-text data-v-83a5a03c">场馆预约</text></view><view class="action-item data-v-83a5a03c" bindtap="{{j}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-sharing data-v-83a5a03c">👥</text></view><text class="action-text data-v-83a5a03c">拼场活动</text></view><view class="action-item data-v-83a5a03c" bindtap="{{k}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-booking data-v-83a5a03c">📅</text></view><text class="action-text data-v-83a5a03c">我的预约</text></view><view class="action-item data-v-83a5a03c" bindtap="{{l}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-venue data-v-83a5a03c">🏟️</text></view><text class="action-text data-v-83a5a03c">场馆列表</text></view><view class="action-item data-v-83a5a03c" bindtap="{{m}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-test data-v-83a5a03c">🧪</text></view><text class="action-text data-v-83a5a03c">API测试</text></view><view class="action-item data-v-83a5a03c" bindtap="{{n}}"><view class="action-icon data-v-83a5a03c"><text class="iconfont icon-user data-v-83a5a03c">👤</text></view><text class="action-text data-v-83a5a03c">个人中心</text></view><view class="action-item pinia-test-item data-v-83a5a03c" bindtap="{{o}}"><view class="action-icon pinia-test-icon data-v-83a5a03c"><text class="iconfont icon-test data-v-83a5a03c">🧪</text></view><text class="action-text data-v-83a5a03c">迁移验证</text></view></view><view class="test-button data-v-83a5a03c" bindtap="{{p}}"><text class="data-v-83a5a03c">🧪 迁移验证</text></view><view class="section data-v-83a5a03c"><view class="section-header data-v-83a5a03c"><text class="section-title data-v-83a5a03c">热门场馆</text><text class="section-more data-v-83a5a03c" bindtap="{{q}}">更多 &gt;</text></view><view class="venue-list data-v-83a5a03c"><view wx:for="{{r}}" wx:for-item="venue" wx:key="i" class="venue-card data-v-83a5a03c" bindtap="{{venue.j}}"><image src="{{venue.a}}" class="venue-image data-v-83a5a03c" mode="aspectFill" lazy-load bindload="{{venue.b}}" binderror="{{venue.c}}"/><view class="venue-info data-v-83a5a03c"><text class="venue-name data-v-83a5a03c">{{venue.d}}</text><text class="venue-location data-v-83a5a03c">{{venue.e}}</text><view class="venue-price data-v-83a5a03c"><text class="price-text data-v-83a5a03c">¥{{venue.f}}/小时</text><text class="{{['venue-status', 'data-v-83a5a03c', venue.h]}}">{{venue.g}}</text></view></view></view><view wx:if="{{s}}" class="no-data data-v-83a5a03c"><text class="no-data-text data-v-83a5a03c">暂无热门场馆数据</text></view></view></view><view class="section data-v-83a5a03c"><view class="section-header data-v-83a5a03c"><text class="section-title data-v-83a5a03c">最新拼场</text><text class="section-more data-v-83a5a03c" bindtap="{{t}}">更多 &gt;</text></view><view class="sharing-list data-v-83a5a03c"><view wx:for="{{v}}" wx:for-item="sharing" wx:key="i" class="sharing-card data-v-83a5a03c" bindtap="{{sharing.j}}"><view class="sharing-header data-v-83a5a03c"><text class="sharing-venue data-v-83a5a03c">{{sharing.a}}</text><text class="sharing-time data-v-83a5a03c">{{sharing.b}} {{sharing.c}}-{{sharing.d}}</text></view><view class="sharing-info data-v-83a5a03c"><text class="sharing-team data-v-83a5a03c">{{sharing.e}}</text><text class="sharing-participants data-v-83a5a03c">{{sharing.f}}/{{sharing.g}}人</text></view><view class="sharing-price data-v-83a5a03c"><text class="price-per-person data-v-83a5a03c">¥{{sharing.h}}/人</text></view></view></view></view></view><view class="floating-test-btn data-v-83a5a03c" bindtap="{{w}}"><text class="floating-btn-text data-v-83a5a03c">🧪</text></view></view>